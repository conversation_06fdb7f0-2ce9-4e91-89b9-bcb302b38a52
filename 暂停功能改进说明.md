# 下载器暂停功能改进说明

## 问题分析

原有的两个下载器（`everia_crawler_db.py` 和 `everia_crawler.py`）在并行下载大量内容时，暂停功能存在以下问题：

1. **响应缓慢**：暂停信号传递不及时，任务无法快速响应停止指令
2. **资源泄漏**：浏览器实例、数据库连接等资源没有被正确清理
3. **任务卡死**：某些长时间运行的任务无法被正常取消
4. **信号处理缺失**：缺乏对 Ctrl+C 等系统信号的处理
5. **强制退出困难**：即使按 Ctrl+C 也无法退出程序

## 改进方案

### 1. 增强信号处理机制

**添加的功能：**
- 导入 `signal` 模块
- 新增 `FORCE_STOP` 事件标志
- 新增 `active_tasks` 集合跟踪所有活跃任务
- 实现 `signal_handler` 函数处理 SIGINT 和 SIGTERM 信号

```python
# 信号处理器
def signal_handler(signum, frame):
    """处理 Ctrl+C 等信号"""
    logger.warning(f"收到信号 {signum}，正在强制停止所有任务...")
    FORCE_STOP.set()
    CRAWL_RUNNING.clear()
    
    # 取消所有活跃任务
    for task in active_tasks.copy():
        if not task.done():
            task.cancel()
    
    # 给一些时间让任务清理
    import time
    time.sleep(2)
    
    logger.info("强制退出程序")
    sys.exit(0)

# 注册信号处理器
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)
```

### 2. 改进任务响应机制

**核心改进：**
- 在所有长时间运行的操作前后检查停止标志
- 使用 `asyncio.wait_for()` 替代无限等待
- 在循环和网络请求中增加停止检查点
- 正确处理 `asyncio.CancelledError` 异常

**示例改进：**
```python
# 改进前
while True:
    page_info = await queue.get()
    if page_info is None: break
    # 处理任务...

# 改进后
while CRAWL_RUNNING.is_set() and not FORCE_STOP.is_set():
    try:
        page_info = await asyncio.wait_for(queue.get(), timeout=1.0)
        if page_info is None: break
        # 处理任务...
    except asyncio.TimeoutError:
        continue  # 超时是正常的，继续检查停止标志
    except asyncio.CancelledError:
        break
```

### 3. 完善资源管理

**资源清理改进：**
- 浏览器实例的安全关闭
- 数据库连接的正确释放
- 临时文件的清理
- 任务集合的维护

```python
# 任务跟踪
task = asyncio.current_task()
active_tasks.add(task)

try:
    # 执行任务...
except asyncio.CancelledError:
    # 处理取消...
finally:
    active_tasks.discard(task)
    # 清理资源...
```

### 4. 增强停止命令处理

**WebSocket 停止命令改进：**
```python
elif action == "stop_crawl" and shared_state['is_crawling']:
    await log_to_ui("收到停止指令，正在安全停止所有任务...", m_type="warning")
    CRAWL_RUNNING.clear()
    
    # 给任务一些时间自然停止
    await asyncio.sleep(1)
    
    # 如果任务还在运行，强制取消
    if shared_state['is_crawling']:
        await log_to_ui("强制取消剩余任务...", m_type="warning")
        FORCE_STOP.set()
        
        # 取消所有活跃任务
        for task in active_tasks.copy():
            if not task.done():
                task.cancel()
        
        # 等待任务清理
        await asyncio.sleep(2)
        
        # 强制更新状态
        await update_shared_state({"status": "已停止", "is_crawling": False, "current_category": ""})
```

### 5. 改进下载任务的取消机制

**下载函数改进：**
- 在下载过程中定期检查停止标志
- 正确处理临时文件清理
- 改进重试机制的取消响应

```python
async for chunk in r.aiter_bytes(65536):
    # 在下载过程中检查停止标志
    if not CRAWL_RUNNING.is_set() or FORCE_STOP.is_set():
        return 'cancelled', 0
    await f.write(chunk)
```

## 测试验证

创建了 `test_pause_functionality.py` 测试脚本，用于验证：
1. 信号处理是否正常工作
2. 任务是否能够快速响应停止信号
3. 资源是否被正确清理
4. 优雅关闭机制是否有效

## 使用说明

### 正常停止
1. 在 Web 界面点击"停止采集"按钮
2. 程序会先尝试优雅停止所有任务
3. 如果任务在1秒内没有停止，会强制取消

### 强制停止
1. 按 `Ctrl+C` 可以立即强制停止所有任务
2. 程序会自动清理资源并退出
3. 不会出现卡死或无法退出的情况

### 运行测试
```bash
python test_pause_functionality.py
```

## 改进效果

1. **响应速度提升**：暂停命令能在1-2秒内生效
2. **资源安全**：所有资源都能被正确清理
3. **强制退出可靠**：Ctrl+C 能够可靠地退出程序
4. **状态一致性**：UI 状态与实际运行状态保持同步
5. **错误处理完善**：各种异常情况都有相应的处理机制

## 注意事项

1. 在大量并发任务运行时，完全停止可能需要几秒钟时间
2. 强制停止会立即终止所有下载，可能导致部分文件不完整
3. 建议在停止后等待几秒钟再重新开始新的下载任务
4. 如果遇到极端情况仍无法退出，可以使用任务管理器强制结束进程

## 技术细节

- 使用 `asyncio.Event` 进行线程安全的状态管理
- 使用 `asyncio.wait_for` 实现超时等待机制
- 使用 `signal` 模块处理系统信号
- 使用 `set` 集合跟踪活跃任务
- 使用 `try-finally` 确保资源清理
- 使用 `asyncio.CancelledError` 处理任务取消
