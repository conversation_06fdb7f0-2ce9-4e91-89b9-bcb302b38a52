from flask import Flask, render_template_string, request, jsonify, session, redirect, url_for, send_from_directory
import os
import json
import hashlib
import sqlite3
from datetime import datetime, timedelta
import random
from werkzeug.security import generate_password_hash, check_password_hash
from functools import wraps
import mimetypes
import sys
import time
import math
from threading import Thread

app = Flask(__name__)
app.secret_key = 'spring_gallery_secret_key_2025'

# 默认头像SVG (内嵌数据URL)
DEFAULT_AVATAR_SVG = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNGRkI2QzEiLz4KPGNpcmNsZSBjeD0iMjAiIGN5PSIxNiIgcj0iNiIgZmlsbD0iI0ZGOTZBQSIvPgo8cGF0aCBkPSJNMTAgMzJDMTAgMjYuNDc3MSAxNC40NzcxIDIyIDE5IDIySDIxQzI1LjUyMjkgMjIgMzAgMjYuNDc3MSAzMCAzMlYzNEgxMFYzMloiIGZpbGw9IiNGRjk2QUEiLz4KPC9zdmc+'

# 占位图SVG (内嵌数据URL)
PLACEHOLDER_SVG = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjVGNUY1Ii8+CjxyZWN0IHg9IjEwIiB5PSIxMCIgd2lkdGg9IjI4MCIgaGVpZ2h0PSIxODAiIHN0cm9rZT0iI0RERERERCIgc3Ryb2tlLXdpZHRoPSIyIiBmaWxsPSJub25lIiBzdHJva2UtZGFzaGFycmF5PSI1LDUiLz4KPGNpcmNsZSBjeD0iMTUwIiBjeT0iODAiIHI9IjIwIiBmaWxsPSIjQ0NDQ0NDIi8+Cjxwb2x5Z29uIHBvaW50cz0iMTMwLDEyMCAxNzAsMTIwIDE1MCwxNDAiIGZpbGw9IiNDQ0NDQ0MiLz4KPHR4dCB4PSIxNTAiIHk9IjE2NSIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjE0IiBmaWxsPSIjOTk5OTk5IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj7lm77niYfmlKDovb3kuK08L3RleHQ+Cjwvc3ZnPg=='

# Favicon SVG (32x32 icon with cherry blossom theme)
FAVICON_SVG = '''<svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
  <rect width="32" height="32" fill="#FFB6C1" rx="6"/>
  <circle cx="16" cy="12" r="3" fill="#FF69B4"/>
  <circle cx="12" cy="16" r="2.5" fill="#FF1493"/>
  <circle cx="20" cy="16" r="2.5" fill="#FF1493"/>
  <circle cx="16" cy="20" r="2" fill="#DC143C"/>
  <circle cx="10" cy="20" r="1.5" fill="#FF69B4"/>
  <circle cx="22" cy="20" r="1.5" fill="#FF69B4"/>
</svg>'''

# 管理面板模板
ADMIN_TEMPLATE = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理面板 - 春色写真馆</title>
    <link rel="icon" type="image/svg+xml" href="/favicon.ico">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }

        /* 移除所有列表标记 */
        ul, ol { list-style: none; }
        li::marker { display: none; }

        body {
            font-family: 'PingFang SC', 'Hiragino Sans GB', 'Noto Sans CJK SC', 'Source Han Sans SC', 'Microsoft YaHei', '微软雅黑', sans-serif;
            background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 25%, #f0fff4 50%, #fff8dc 75%, #fdf5e6 100%);
            min-height: 100vh;
            padding: 20px;
            color: #5d4e37;
        }
        .admin-container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 4px 15px rgba(255, 182, 193, 0.2);
            border: 1px solid rgba(255, 182, 193, 0.1);
        }
        .admin-header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid rgba(255, 182, 193, 0.3);
        }
        .admin-header h1 {
            color: #8b4513;
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(255, 182, 193, 0.3);
        }
        .admin-header p {
            color: #5d4e37;
            font-size: 1.1em;
        }
        .task-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }
        .task-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(5px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(255, 182, 193, 0.2);
            border: 1px solid rgba(255, 182, 193, 0.2);
            transition: all 0.3s ease;
        }
        .task-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 182, 193, 0.3);
        }
        .task-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        .task-icon {
            font-size: 2em;
            margin-right: 15px;
        }
        .task-title {
            font-size: 1.4em;
            font-weight: bold;
            color: #8b4513;
        }
        .task-status {
            margin-bottom: 15px;
        }
        .status-badge {
            display: inline-block;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-idle {
            background: rgba(255, 182, 193, 0.2);
            color: #8b4513;
            border: 1px solid rgba(255, 182, 193, 0.4);
        }
        .status-running {
            background: rgba(255, 218, 185, 0.3);
            color: #d2691e;
            border: 1px solid rgba(255, 218, 185, 0.6);
        }
        .status-completed {
            background: rgba(144, 238, 144, 0.3);
            color: #228b22;
            border: 1px solid rgba(144, 238, 144, 0.6);
        }
        .status-error {
            background: rgba(255, 182, 193, 0.4);
            color: #8b0000;
            border: 1px solid rgba(255, 182, 193, 0.7);
        }
        .progress-container {
            margin: 15px 0;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: rgba(255, 182, 193, 0.2);
            border-radius: 10px;
            overflow: hidden;
            border: 1px solid rgba(255, 182, 193, 0.3);
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff69b4, #ff1493);
            transition: width 0.3s ease;
            border-radius: 10px;
        }
        .progress-text {
            margin-top: 5px;
            font-size: 0.9em;
            color: #5d4e37;
        }
        .task-button {
            width: 100%;
            padding: 12px 20px;
            border: none;
            border-radius: 10px;
            font-size: 1.1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 10px;
        }
        .btn-primary {
            background: linear-gradient(135deg, #ff69b4 0%, #ff1493 100%);
            color: white;
            box-shadow: 0 2px 10px rgba(255, 105, 180, 0.3);
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 105, 180, 0.4);
        }
        .btn-primary:disabled {
            background: rgba(255, 182, 193, 0.5);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        .task-info {
            margin-top: 15px;
            padding: 15px;
            background: rgba(255, 240, 245, 0.7);
            border-radius: 10px;
            font-size: 0.9em;
            border: 1px solid rgba(255, 182, 193, 0.3);
            color: #5d4e37;
        }
        .last-run {
            color: #5d4e37;
            margin-bottom: 10px;
        }
        .logs-container {
            max-height: 200px;
            overflow-y: auto;
            background: rgba(255, 240, 245, 0.5);
            border-radius: 8px;
            padding: 10px;
            font-family: monospace;
            font-size: 0.8em;
            line-height: 1.4;
            border: 1px solid rgba(255, 182, 193, 0.3);
        }
        .log-entry {
            margin-bottom: 5px;
            color: #5d4e37;
        }
        .back-button { display: inline-block; padding: 10px 20px; background: #6c757d; color: white; text-decoration: none; border-radius: 10px; margin-top: 20px; transition: all 0.3s ease; }
        .back-button:hover { background: #5a6268; transform: translateY(-2px); }

        /* Mobile responsive styles */
        @media (max-width: 768px) {
            .admin-container {
                padding: 10px;
                margin: 0;
            }

            .admin-header h1 {
                font-size: 1.8rem;
            }

            .task-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .task-card {
                padding: 15px;
            }

            .task-header {
                flex-direction: column;
                text-align: center;
                gap: 10px;
            }

            .task-icon {
                font-size: 2rem;
            }

            .task-title {
                font-size: 1.1rem;
            }

            .task-button {
                font-size: 1rem;
                padding: 10px 15px;
            }

            .logs-container {
                max-height: 150px;
                font-size: 0.75em;
            }

            .progress-container {
                margin: 10px 0;
            }

            .task-info {
                padding: 10px;
                font-size: 0.85em;
            }
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <div class="admin-header">
            <h1>🛠️ 管理面板</h1>
            <p>系统维护和管理工具</p>
        </div>
        <div class="task-grid">
            <div class="task-card">
                <div class="task-header">
                    <div class="task-icon">📚</div>
                    <div class="task-title">图库扫描</div>
                </div>
                <div class="task-status">
                    <span id="scan-status" class="status-badge status-idle">待机</span>
                </div>
                <div class="progress-container">
                    <div class="progress-bar">
                        <div id="scan-progress" class="progress-fill" style="width: 0%"></div>
                    </div>
                    <div id="scan-progress-text" class="progress-text">0 / 0</div>
                </div>
                <button id="scan-button" class="task-button btn-primary" onclick="startLibraryScan()">开始图库扫描</button>
                <div class="task-info">
                    <div id="scan-last-run" class="last-run">上次运行: 从未运行</div>
                    <div id="scan-message">扫描并更新图库中的所有图片信息</div>
                    <div class="logs-container"><div id="scan-logs">暂无日志</div></div>
                </div>
            </div>
            <div class="task-card">
                <div class="task-header">
                    <div class="task-icon">🔗</div>
                    <div class="task-title">路径修正</div>
                </div>
                <div class="task-status">
                    <span id="path-status" class="status-badge status-idle">待机</span>
                </div>
                <div class="progress-container">
                    <div class="progress-bar">
                        <div id="path-progress" class="progress-fill" style="width: 0%"></div>
                    </div>
                    <div id="path-progress-text" class="progress-text">0 / 0</div>
                </div>
                <button id="path-button" class="task-button btn-primary" onclick="startPathFix()">修正图片路径</button>
                <div class="task-info">
                    <div id="path-last-run" class="last-run">上次运行: 从未运行</div>
                    <div id="path-message">修正数据库中的旧格式图片路径</div>
                    <div class="logs-container"><div id="path-logs">暂无日志</div></div>
                </div>
            </div>
            <div class="task-card">
                <div class="task-header">
                    <div class="task-icon">🔧</div>
                    <div class="task-title">文件名修复</div>
                </div>
                <div class="task-status">
                    <span id="fix-status" class="status-badge status-idle">待机</span>
                </div>
                <div class="progress-container">
                    <div class="progress-bar">
                        <div id="fix-progress" class="progress-fill" style="width: 0%"></div>
                    </div>
                    <div id="fix-progress-text" class="progress-text">0 / 0</div>
                </div>
                <button id="fix-button" class="task-button btn-primary" onclick="startFilenameFix()">检查并修复文件名</button>
                <div class="task-info">
                    <div id="fix-last-run" class="last-run">上次运行: 从未运行</div>
                    <div id="fix-message">检查并修复URL编码的文件名</div>
                    <div class="logs-container"><div id="fix-logs">暂无日志</div></div>
                </div>
            </div>
        </div>
        <a href="/" class="back-button">← 返回主页</a>
    </div>
    <script>
        let updateInterval;
        function updateStatus() {
            fetch('/api/admin/status').then(response => response.json()).then(data => {
                updateTaskStatus('scan', data.library_scan);
                updateTaskStatus('path', data.path_fix || {status: 'idle', progress: 0, current: 0, total: 0, message: '', logs: []});
                updateTaskStatus('fix', data.filename_fix);
            }).catch(error => console.error('Error:', error));
        }
        function updateTaskStatus(taskType, taskData) {
            const statusElement = document.getElementById(taskType + '-status');
            const progressElement = document.getElementById(taskType + '-progress');
            const progressTextElement = document.getElementById(taskType + '-progress-text');
            const buttonElement = document.getElementById(taskType + '-button');
            const lastRunElement = document.getElementById(taskType + '-last-run');
            const messageElement = document.getElementById(taskType + '-message');
            const logsElement = document.getElementById(taskType + '-logs');
            statusElement.className = 'status-badge status-' + taskData.status;
            statusElement.textContent = getStatusText(taskData.status);
            progressElement.style.width = taskData.progress + '%';
            progressTextElement.textContent = taskData.current + ' / ' + taskData.total;
            buttonElement.disabled = taskData.status === 'running';
            if (taskData.status === 'running') {
                buttonElement.textContent = '运行中...';
            } else {
                const buttonTexts = {
                    'scan': '开始图库扫描',
                    'path': '修正图片路径',
                    'fix': '检查并修复文件名'
                };
                buttonElement.textContent = buttonTexts[taskType] || '开始任务';
            }
            if (taskData.last_run) {
                lastRunElement.textContent = '上次运行: ' + taskData.last_run;
            }
            if (taskData.message) {
                messageElement.textContent = taskData.message;
            }
            if (taskData.logs && taskData.logs.length > 0) {
                logsElement.innerHTML = taskData.logs.map(log => '<div class="log-entry">' + log + '</div>').join('');
                logsElement.scrollTop = logsElement.scrollHeight;
            }
        }
        function getStatusText(status) {
            const statusMap = { 'idle': '待机', 'running': '运行中', 'completed': '已完成', 'error': '错误' };
            return statusMap[status] || status;
        }
        function startLibraryScan() {
            fetch('/api/admin/library-scan', { method: 'POST' }).then(response => response.json()).then(data => {
                if (data.success) { startStatusUpdates(); } else { alert('启动失败: ' + data.message); }
            }).catch(error => { console.error('Error:', error); alert('启动失败'); });
        }
        function startPathFix() {
            fetch('/api/admin/path-fix', { method: 'POST' }).then(response => response.json()).then(data => {
                if (data.success) { startStatusUpdates(); } else { alert('启动失败: ' + data.message); }
            }).catch(error => { console.error('Error:', error); alert('启动失败'); });
        }
        function startFilenameFix() {
            fetch('/api/admin/filename-fix', { method: 'POST' }).then(response => response.json()).then(data => {
                if (data.success) { startStatusUpdates(); } else { alert('启动失败: ' + data.message); }
            }).catch(error => { console.error('Error:', error); alert('启动失败'); });
        }
        function startStatusUpdates() {
            if (updateInterval) { clearInterval(updateInterval); }
            updateInterval = setInterval(updateStatus, 1000);
        }
        document.addEventListener('DOMContentLoaded', function() { updateStatus(); startStatusUpdates(); });
        window.addEventListener('beforeunload', function() { if (updateInterval) { clearInterval(updateInterval); } });
    </script>
</body>
</html>
'''

# 全局变量存储扫描状态
scan_progress = {'current': 0, 'total': 0, 'status': '准备扫描...', 'completed': False}

# 管理任务状态
admin_tasks = {
    "library_scan": {
        "status": "idle",  # idle, running, completed, error
        "progress": 0,
        "current": 0,
        "total": 0,
        "message": "",
        "last_run": None,
        "logs": []
    },
    "filename_fix": {
        "status": "idle",
        "progress": 0,
        "current": 0,
        "total": 0,
        "message": "",
        "last_run": None,
        "logs": []
    }
}

# 数据库初始化
def init_db():
    conn = sqlite3.connect('gallery.db')
    c = conn.cursor()
    
    # 用户表（移除email字段）
    c.execute('''CREATE TABLE IF NOT EXISTS users
                 (id INTEGER PRIMARY KEY AUTOINCREMENT,
                  username TEXT UNIQUE NOT NULL,
                  password_hash TEXT NOT NULL,
                  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                  avatar TEXT)''')

    # 检查并迁移现有数据库结构
    try:
        c.execute("PRAGMA table_info(users)")
        columns = [column[1] for column in c.fetchall()]
        if 'email' in columns:
            # 如果存在email字段，创建新表并迁移数据
            c.execute('''CREATE TABLE IF NOT EXISTS users_new
                         (id INTEGER PRIMARY KEY AUTOINCREMENT,
                          username TEXT UNIQUE NOT NULL,
                          password_hash TEXT NOT NULL,
                          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                          avatar TEXT)''')

            # 迁移数据
            c.execute('''INSERT INTO users_new (id, username, password_hash, created_at, avatar)
                         SELECT id, username, password_hash, created_at, avatar FROM users''')

            # 删除旧表并重命名新表
            c.execute('DROP TABLE users')
            c.execute('ALTER TABLE users_new RENAME TO users')
    except sqlite3.Error:
        # 如果迁移失败，继续使用现有结构
        pass
    
    # 图片表
    c.execute('''CREATE TABLE IF NOT EXISTS images
                 (id INTEGER PRIMARY KEY AUTOINCREMENT,
                  filename TEXT NOT NULL,
                  path TEXT NOT NULL,
                  full_path TEXT NOT NULL,
                  category TEXT NOT NULL,
                  album TEXT NOT NULL,
                  file_size INTEGER,
                  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP)''')
    
    # 收藏表
    c.execute('''CREATE TABLE IF NOT EXISTS favorites
                 (id INTEGER PRIMARY KEY AUTOINCREMENT,
                  user_id INTEGER,
                  image_id INTEGER,
                  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                  FOREIGN KEY (user_id) REFERENCES users (id),
                  FOREIGN KEY (image_id) REFERENCES images (id))''')

    # 图集收藏表
    c.execute('''CREATE TABLE IF NOT EXISTS album_favorites
                 (id INTEGER PRIMARY KEY AUTOINCREMENT,
                  user_id INTEGER,
                  category TEXT,
                  album TEXT,
                  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                  FOREIGN KEY (user_id) REFERENCES users (id),
                  UNIQUE(user_id, category, album))''')

    # 检查并添加缺失的列（数据库迁移）
    try:
        c.execute("SELECT user_id FROM album_favorites LIMIT 1")
    except sqlite3.OperationalError:
        # user_id列不存在，添加它
        c.execute("ALTER TABLE album_favorites ADD COLUMN user_id INTEGER")
        c.execute("CREATE UNIQUE INDEX IF NOT EXISTS idx_album_favorites_unique ON album_favorites(user_id, category, album)")
    
    # 评论表
    c.execute('''CREATE TABLE IF NOT EXISTS comments
                 (id INTEGER PRIMARY KEY AUTOINCREMENT,
                  user_id INTEGER,
                  image_id INTEGER,
                  content TEXT,
                  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                  FOREIGN KEY (user_id) REFERENCES users (id),
                  FOREIGN KEY (image_id) REFERENCES images (id))''')
    
    # 评分表
    c.execute('''CREATE TABLE IF NOT EXISTS ratings
                 (id INTEGER PRIMARY KEY AUTOINCREMENT,
                  user_id INTEGER,
                  image_id INTEGER,
                  rating INTEGER CHECK(rating >= 1 AND rating <= 5),
                  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                  FOREIGN KEY (user_id) REFERENCES users (id),
                  FOREIGN KEY (image_id) REFERENCES images (id))''')
    
    # 创建索引
    c.execute('CREATE INDEX IF NOT EXISTS idx_images_category ON images(category)')
    c.execute('CREATE INDEX IF NOT EXISTS idx_images_album ON images(album)')
    c.execute('CREATE INDEX IF NOT EXISTS idx_images_category_album ON images(category, album)')
    c.execute('CREATE INDEX IF NOT EXISTS idx_favorites_user ON favorites(user_id)')
    c.execute('CREATE INDEX IF NOT EXISTS idx_favorites_image ON favorites(image_id)')
    c.execute('CREATE INDEX IF NOT EXISTS idx_comments_image ON comments(image_id)')
    c.execute('CREATE INDEX IF NOT EXISTS idx_comments_user ON comments(user_id)')
    
    conn.commit()
    conn.close()

def print_progress_bar(current, total, status="", bar_length=50):
    """打印进度条"""
    if total == 0:
        percent = 0
    else:
        percent = current / total
    
    filled_length = int(bar_length * percent)
    bar = '█' * filled_length + '░' * (bar_length - filled_length)
    
    # 清除当前行并打印新的进度条
    sys.stdout.write(f'\r🌸 [{bar}] {percent:.1%} ({current}/{total}) {status}')
    sys.stdout.flush()

def get_existing_albums():
    """获取数据库中已存在的图集"""
    conn = sqlite3.connect('gallery.db')
    c = conn.cursor()
    c.execute('SELECT DISTINCT category, album FROM images')
    existing = set()
    for row in c.fetchall():
        existing.add((row[0], row[1]))
    conn.close()
    return existing

def check_album_integrity(category, album, base_path):
    """检查图集完整性，通过检查第一个图片文件是否存在"""
    album_path = os.path.join(base_path, category, album)
    if not os.path.exists(album_path):
        return False

    # 获取图集中的第一个图片文件
    image_files = []
    for file in os.listdir(album_path):
        if file.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.webp')):
            image_files.append(file)

    if not image_files:
        return False

    # 检查第一个图片文件是否存在
    first_image = sorted(image_files)[0]
    first_image_path = os.path.join(album_path, first_image)
    return os.path.exists(first_image_path)

def album_exists_and_complete(album_path):
    """检查图集是否存在且完整"""
    if not os.path.exists(album_path):
        return False

    try:
        # 获取图集中的图片文件
        image_files = []
        for file in os.listdir(album_path):
            if file.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp')):
                image_files.append(file)

        # 如果没有图片文件，认为不完整
        if not image_files:
            return False

        # 检查文件夹是否可读
        if not os.access(album_path, os.R_OK):
            return False

        # 检查随机几个图片文件是否存在且可读
        import random
        check_count = min(5, len(image_files))  # 检查最多5个文件
        files_to_check = random.sample(image_files, check_count) if len(image_files) > check_count else image_files

        for file in files_to_check:
            file_path = os.path.join(album_path, file)
            if not os.path.exists(file_path) or not os.access(file_path, os.R_OK):
                return False

            # 检查文件大小，如果文件为空或过小，认为损坏
            try:
                file_size = os.path.getsize(file_path)
                if file_size < 1024:  # 小于1KB认为可能损坏
                    return False
            except OSError:
                return False

        return True
    except (OSError, PermissionError):
        return False

def fix_shit_encoded_files_admin():
    """管理员触发的文件名修复"""
    global admin_tasks
    import urllib.parse
    import shutil
    import time

    downloaded_dir = "downloaded"
    db_path = "gallery.db"

    if not os.path.exists(downloaded_dir) or not os.path.exists(db_path):
        admin_tasks["filename_fix"]["message"] = "目录或数据库不存在"
        admin_tasks["filename_fix"]["status"] = "error"
        return

    def is_shit_filename(filename):
        """判断是否是狗屎URL编码文件名"""
        return '%' in filename and any(pattern in filename for pattern in [
            '%25', '%2B', '%28', '%29', '%20', '%E', '%F'
        ])

    def decode_shit_filename(filename):
        """解码狗屎文件名为正常文件名"""
        current = filename
        for i in range(10):  # 最多10次解码
            try:
                decoded = urllib.parse.unquote(current, encoding='utf-8')
                if decoded == current:
                    break
                current = decoded
            except:
                break

        # 清理非法字符
        illegal_chars = '<>:"|?*\\'
        for char in illegal_chars:
            current = current.replace(char, '_')

        return current

    # 快速扫描检查是否有狗屎文件
    admin_tasks["filename_fix"]["message"] = "快速检查是否需要修复..."
    has_shit_files = False
    sample_count = 0

    for root, dirs, files in os.walk(downloaded_dir):
        for filename in files:
            sample_count += 1
            if is_shit_filename(filename):
                has_shit_files = True
                break
            # 只检查前1000个文件，如果没有就跳过
            if sample_count > 1000:
                break
        if has_shit_files:
            break

    if not has_shit_files:
        admin_tasks["filename_fix"]["message"] = "没有发现URL编码文件"
        admin_tasks["filename_fix"]["status"] = "completed"
        admin_tasks["filename_fix"]["logs"].append("没有发现需要修复的URL编码文件")
        return

    admin_tasks["filename_fix"]["logs"].append("发现URL编码文件，开始修复...")

    # 查找所有狗屎文件
    shit_files = []
    total_files = 0

    # 先统计总文件数
    for root, dirs, files in os.walk(downloaded_dir):
        total_files += len(files)

    admin_tasks["filename_fix"]["total"] = total_files
    admin_tasks["filename_fix"]["logs"].append(f"扫描 {total_files} 个文件...")

    current_file = 0
    for root, dirs, files in os.walk(downloaded_dir):
        for filename in files:
            current_file += 1
            if current_file % 100 == 0:  # 每100个文件更新一次进度
                admin_tasks["filename_fix"]["current"] = current_file
                admin_tasks["filename_fix"]["progress"] = int(100 * current_file / total_files)
                admin_tasks["filename_fix"]["message"] = f"扫描文件 {current_file}/{total_files}"

            if is_shit_filename(filename):
                full_path = os.path.join(root, filename)
                decoded_filename = decode_shit_filename(filename)
                new_full_path = os.path.join(root, decoded_filename)

                if full_path != new_full_path:  # 只处理需要重命名的文件
                    shit_files.append({
                        'old_path': full_path,
                        'new_path': new_full_path,
                        'old_filename': filename,
                        'new_filename': decoded_filename,
                        'old_relative': os.path.relpath(full_path, downloaded_dir).replace('\\', '/'),
                        'new_relative': os.path.relpath(new_full_path, downloaded_dir).replace('\\', '/')
                    })

    if not shit_files:
        admin_tasks["filename_fix"]["message"] = "没有发现需要修复的URL编码文件"
        admin_tasks["filename_fix"]["status"] = "completed"
        admin_tasks["filename_fix"]["logs"].append("没有发现需要修复的URL编码文件")
        return

    admin_tasks["filename_fix"]["logs"].append(f"发现 {len(shit_files)} 个需要修复的文件")

    # 备份数据库
    backup_db = f"{db_path}.auto_backup"
    try:
        shutil.copy2(db_path, backup_db)
        admin_tasks["filename_fix"]["logs"].append(f"数据库已备份到: {backup_db}")
    except Exception as e:
        admin_tasks["filename_fix"]["logs"].append(f"数据库备份失败: {e}")

    # 重命名文件
    admin_tasks["filename_fix"]["message"] = "重命名文件..."
    renamed_count = 0
    for i, file_info in enumerate(shit_files):
        admin_tasks["filename_fix"]["current"] = i + 1
        admin_tasks["filename_fix"]["progress"] = int(100 * (i + 1) / len(shit_files))
        admin_tasks["filename_fix"]["message"] = f"重命名文件 {i + 1}/{len(shit_files)}"

        try:
            if not os.path.exists(file_info['new_path']):  # 避免覆盖现有文件
                os.rename(file_info['old_path'], file_info['new_path'])
                renamed_count += 1
        except Exception as e:
            admin_tasks["filename_fix"]["logs"].append(f"重命名失败: {file_info['old_filename']} - {e}")

    # 批量更新数据库
    if renamed_count > 0:
        admin_tasks["filename_fix"]["message"] = "更新数据库..."
        try:
            conn = sqlite3.connect(db_path, timeout=30)  # 设置超时
            cursor = conn.cursor()
            updated_count = 0

            # 批量更新，每次处理100条记录
            batch_size = 100
            for i in range(0, len(shit_files), batch_size):
                batch = shit_files[i:i + batch_size]
                admin_tasks["filename_fix"]["message"] = f"更新数据库 {min(i + batch_size, len(shit_files))}/{len(shit_files)}"

                for file_info in batch:
                    if os.path.exists(file_info['new_path']):
                        try:
                            cursor.execute("""
                                UPDATE images
                                SET path = ?, filename = ?, full_path = ?
                                WHERE filename = ?
                            """, (
                                file_info['new_relative'],
                                file_info['new_filename'],
                                file_info['new_relative'],
                                file_info['old_filename']
                            ))

                            if cursor.rowcount > 0:
                                updated_count += 1
                        except Exception as e:
                            admin_tasks["filename_fix"]["logs"].append(f"数据库更新失败: {file_info['old_filename']} - {e}")

                # 每批次提交一次
                conn.commit()

            conn.close()
            admin_tasks["filename_fix"]["logs"].append(f"数据库更新完成: {updated_count} 条记录")

        except Exception as e:
            admin_tasks["filename_fix"]["logs"].append(f"数据库更新失败: {e}")

    admin_tasks["filename_fix"]["logs"].append(f"文件名修复完成: 重命名了 {renamed_count} 个文件")

def fix_shit_encoded_files():
    """启动时自动修复URL编码的狗屎文件名 - 优化版本"""
    import urllib.parse
    import shutil
    import time

    downloaded_dir = "downloaded"
    db_path = "gallery.db"
    fix_flag_file = "filename_fix_completed.flag"

    # 检查是否已经修复过
    if os.path.exists(fix_flag_file):
        print("   ✅ 文件名已修复过，跳过检查")
        return

    if not os.path.exists(downloaded_dir) or not os.path.exists(db_path):
        print("   ⚠️ 跳过文件名修复: 目录或数据库不存在")
        return

    def is_shit_filename(filename):
        """判断是否是狗屎URL编码文件名"""
        return '%' in filename and any(pattern in filename for pattern in [
            '%25', '%2B', '%28', '%29', '%20', '%E', '%F'
        ])

    def decode_shit_filename(filename):
        """解码狗屎文件名为正常文件名"""
        current = filename
        for i in range(10):  # 最多10次解码
            try:
                decoded = urllib.parse.unquote(current, encoding='utf-8')
                if decoded == current:
                    break
                current = decoded
            except:
                break

        # 清理非法字符
        illegal_chars = '<>:"|?*\\'
        for char in illegal_chars:
            current = current.replace(char, '_')

        return current

    def print_progress(current, total, prefix="进度"):
        """打印进度条"""
        if total == 0:
            return
        percent = int(100 * current / total)
        bar_length = 30
        filled_length = int(bar_length * current / total)
        bar = '█' * filled_length + '░' * (bar_length - filled_length)
        print(f"\r   {prefix}: [{bar}] {percent}% ({current}/{total})", end='', flush=True)

    # 快速扫描检查是否有狗屎文件
    print("   🔍 快速检查是否需要修复...")
    has_shit_files = False
    sample_count = 0

    for root, dirs, files in os.walk(downloaded_dir):
        for filename in files:
            sample_count += 1
            if is_shit_filename(filename):
                has_shit_files = True
                break
            # 只检查前1000个文件，如果没有就跳过
            if sample_count > 1000:
                break
        if has_shit_files:
            break

    if not has_shit_files:
        print("   ✅ 没有发现URL编码文件，创建完成标记")
        # 创建完成标记文件
        with open(fix_flag_file, 'w') as f:
            f.write(f"文件名修复完成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        return

    print("   🔧 发现URL编码文件，开始修复...")

    # 查找所有狗屎文件
    shit_files = []
    total_files = 0

    # 先统计总文件数
    for root, dirs, files in os.walk(downloaded_dir):
        total_files += len(files)

    print(f"   📊 扫描 {total_files} 个文件...")

    current_file = 0
    for root, dirs, files in os.walk(downloaded_dir):
        for filename in files:
            current_file += 1
            if current_file % 100 == 0:  # 每100个文件更新一次进度
                print_progress(current_file, total_files, "扫描")

            if is_shit_filename(filename):
                full_path = os.path.join(root, filename)
                decoded_filename = decode_shit_filename(filename)
                new_full_path = os.path.join(root, decoded_filename)

                if full_path != new_full_path:  # 只处理需要重命名的文件
                    shit_files.append({
                        'old_path': full_path,
                        'new_path': new_full_path,
                        'old_filename': filename,
                        'new_filename': decoded_filename,
                        'old_relative': os.path.relpath(full_path, downloaded_dir).replace('\\', '/'),
                        'new_relative': os.path.relpath(new_full_path, downloaded_dir).replace('\\', '/')
                    })

    print()  # 换行

    if not shit_files:
        print("   ✅ 没有发现需要修复的URL编码文件")
        # 创建完成标记文件
        with open(fix_flag_file, 'w') as f:
            f.write(f"文件名修复完成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        return

    print(f"   🔧 发现 {len(shit_files)} 个需要修复的文件")

    # 备份数据库
    backup_db = f"{db_path}.auto_backup"
    try:
        shutil.copy2(db_path, backup_db)
        print(f"   💾 数据库已备份到: {backup_db}")
    except Exception as e:
        print(f"   ⚠️ 数据库备份失败: {e}")

    # 重命名文件
    print("   📝 重命名文件...")
    renamed_count = 0
    for i, file_info in enumerate(shit_files):
        print_progress(i + 1, len(shit_files), "重命名")

        try:
            if not os.path.exists(file_info['new_path']):  # 避免覆盖现有文件
                os.rename(file_info['old_path'], file_info['new_path'])
                renamed_count += 1
        except Exception as e:
            print(f"\n   ❌ 重命名失败: {file_info['old_filename']} - {e}")

    print()  # 换行

    # 批量更新数据库
    if renamed_count > 0:
        print("   💾 更新数据库...")
        try:
            conn = sqlite3.connect(db_path, timeout=30)  # 设置超时
            cursor = conn.cursor()
            updated_count = 0

            # 批量更新，每次处理100条记录
            batch_size = 100
            for i in range(0, len(shit_files), batch_size):
                batch = shit_files[i:i + batch_size]
                print_progress(min(i + batch_size, len(shit_files)), len(shit_files), "数据库")

                for file_info in batch:
                    if os.path.exists(file_info['new_path']):
                        try:
                            cursor.execute("""
                                UPDATE images
                                SET path = ?, filename = ?, full_path = ?
                                WHERE filename = ?
                            """, (
                                file_info['new_relative'],
                                file_info['new_filename'],
                                file_info['new_relative'],
                                file_info['old_filename']
                            ))

                            if cursor.rowcount > 0:
                                updated_count += 1
                        except Exception as e:
                            print(f"\n   ❌ 数据库更新失败: {file_info['old_filename']} - {e}")

                # 每批次提交一次
                conn.commit()

            conn.close()
            print(f"\n   ✅ 数据库更新完成: {updated_count} 条记录")

        except Exception as e:
            print(f"\n   ❌ 数据库更新失败: {e}")

    # 创建完成标记文件
    try:
        with open(fix_flag_file, 'w') as f:
            f.write(f"文件名修复完成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"修复文件数量: {renamed_count}\n")
        print(f"   🎉 文件名修复完成: 重命名了 {renamed_count} 个文件")
    except Exception as e:
        print(f"   ⚠️ 创建完成标记失败: {e}")
        print(f"   🎉 文件名修复完成: 重命名了 {renamed_count} 个文件")

def scan_and_store_images_admin():
    """管理员触发的图库扫描"""
    global admin_tasks

    base_path = 'downloaded'
    categories = ['korea', 'cosplay', 'japan', 'gravure', 'chinese', 'thailand']

    if not os.path.exists(base_path):
        admin_tasks["library_scan"]["message"] = "下载目录不存在"
        admin_tasks["library_scan"]["status"] = "error"
        return

    # 获取已存在的图集
    existing_albums = get_existing_albums()
    admin_tasks["library_scan"]["logs"].append(f"数据库中已有 {len(existing_albums)} 个图集")

    # 修正数据库中的旧路径格式
    admin_tasks["library_scan"]["message"] = "修正数据库路径格式..."
    admin_tasks["library_scan"]["logs"].append("开始修正数据库中的旧路径格式")

    conn = sqlite3.connect('gallery.db')
    c = conn.cursor()

    # 查找所有不以 /images/ 开头的路径
    c.execute("SELECT id, path, full_path FROM images WHERE path NOT LIKE '/images/%'")
    old_paths = c.fetchall()

    if old_paths:
        admin_tasks["library_scan"]["logs"].append(f"发现 {len(old_paths)} 条旧格式路径，正在修正...")

        import urllib.parse
        for image_id, old_path, old_full_path in old_paths:
            # 生成新的路径格式
            if old_path.startswith('/'):
                # 如果已经是绝对路径但不是 /images/ 开头，添加 /images 前缀
                new_path = f"/images{old_path}"
                new_full_path = f"/images{old_full_path}" if old_full_path else new_path
            else:
                # 如果是相对路径，添加 /images/ 前缀
                new_path = f"/images/{old_path}"
                new_full_path = f"/images/{old_full_path}" if old_full_path else new_path

            # 更新数据库
            c.execute("UPDATE images SET path = ?, full_path = ? WHERE id = ?",
                     (new_path, new_full_path, image_id))

        conn.commit()
        admin_tasks["library_scan"]["logs"].append(f"✅ 已修正 {len(old_paths)} 条路径格式")
    else:
        admin_tasks["library_scan"]["logs"].append("✅ 所有路径格式都是最新的")

    conn.close()

    # 验证数据库中现有图集的完整性
    admin_tasks["library_scan"]["message"] = "验证现有图集完整性..."
    invalid_albums = []
    valid_albums = []

    for category, album in existing_albums:
        album_path = os.path.join(base_path, category, album)
        if not album_exists_and_complete(album_path):
            invalid_albums.append((category, album))
            admin_tasks["library_scan"]["logs"].append(f"⚠️ 图集已损坏或丢失: {category}/{album}")
        else:
            valid_albums.append((category, album))

    if invalid_albums:
        admin_tasks["library_scan"]["logs"].append(f"发现 {len(invalid_albums)} 个损坏/丢失的图集，将从数据库中清理")

        # 清理损坏的图集记录和相关收藏
        conn = sqlite3.connect('gallery.db')
        c = conn.cursor()

        total_favorites_removed = 0
        total_image_favorites_removed = 0

        for category, album in invalid_albums:
            # 检查是否有用户收藏了这个图集
            c.execute('SELECT COUNT(*) FROM album_favorites WHERE category = ? AND album = ?', (category, album))
            album_favorites_count = c.fetchone()[0]

            # 检查是否有用户收藏了这个图集中的图片
            c.execute('SELECT COUNT(*) FROM favorites f JOIN images i ON f.image_id = i.id WHERE i.category = ? AND i.album = ?', (category, album))
            image_favorites_count = c.fetchone()[0]

            # 删除图集收藏记录
            if album_favorites_count > 0:
                c.execute('DELETE FROM album_favorites WHERE category = ? AND album = ?', (category, album))
                total_favorites_removed += album_favorites_count
                admin_tasks["library_scan"]["logs"].append(f"📌 已清理图集收藏: {category}/{album} ({album_favorites_count} 个用户收藏)")

            # 删除图片收藏记录
            if image_favorites_count > 0:
                c.execute('DELETE FROM favorites WHERE image_id IN (SELECT id FROM images WHERE category = ? AND album = ?)', (category, album))
                total_image_favorites_removed += image_favorites_count
                admin_tasks["library_scan"]["logs"].append(f"🖼️ 已清理图片收藏: {category}/{album} ({image_favorites_count} 张图片收藏)")

            # 删除图集记录
            c.execute('DELETE FROM images WHERE category = ? AND album = ?', (category, album))
            admin_tasks["library_scan"]["logs"].append(f"🗑️ 已清理损坏图集: {category}/{album}")

        conn.commit()
        conn.close()

        # 汇总清理结果
        if total_favorites_removed > 0 or total_image_favorites_removed > 0:
            admin_tasks["library_scan"]["logs"].append(f"⚠️ 收藏清理汇总: 清理了 {total_favorites_removed} 个图集收藏，{total_image_favorites_removed} 个图片收藏")
            admin_tasks["library_scan"]["logs"].append("💡 提示: 由于原文件丢失，相关收藏记录已被自动清理")
            admin_tasks["library_scan"]["logs"].append("📢 建议: 请通知用户相关图集已不可用，收藏记录已清理")
        else:
            admin_tasks["library_scan"]["logs"].append("✅ 无收藏记录需要清理")

    if invalid_albums:
        admin_tasks["library_scan"]["logs"].append(f"验证完成: {len(valid_albums)} 个图集完整，{len(invalid_albums)} 个图集已清理")
    else:
        admin_tasks["library_scan"]["logs"].append(f"验证完成: 所有 {len(valid_albums)} 个图集都完整")

    # 统计需要扫描的文件
    admin_tasks["library_scan"]["message"] = "分析需要扫描的内容..."
    admin_tasks["library_scan"]["current"] = 0
    admin_tasks["library_scan"]["total"] = 0

    albums_to_scan = []
    total_files = 0

    for category in categories:
        category_path = os.path.join(base_path, category)
        if os.path.exists(category_path):
            for album in os.listdir(category_path):
                album_path = os.path.join(category_path, album)
                if os.path.isdir(album_path):
                    # 检查是否需要扫描
                    if (category, album) not in existing_albums or not album_exists_and_complete(album_path):
                        album_files = []
                        for file in os.listdir(album_path):
                            if file.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp')):
                                album_files.append(file)

                        if album_files:
                            albums_to_scan.append((category, album, album_files))
                            total_files += len(album_files)

    admin_tasks["library_scan"]["total"] = total_files

    if total_files == 0:
        admin_tasks["library_scan"]["message"] = "扫描完成"
        admin_tasks["library_scan"]["status"] = "completed"
        if invalid_albums:
            admin_tasks["library_scan"]["logs"].append(f"✅ 扫描完成: 清理了 {len(invalid_albums)} 个损坏图集及相关收藏，无新内容需要扫描")
        else:
            admin_tasks["library_scan"]["logs"].append("✅ 扫描完成: 所有图集都完整且是最新状态，无需扫描")
        return

    admin_tasks["library_scan"]["logs"].append(f"需要扫描 {len(albums_to_scan)} 个图集，共 {total_files} 个文件")

    conn = sqlite3.connect('gallery.db')
    c = conn.cursor()

    processed = 0

    # 扫描需要更新的图集
    for category, album, files in albums_to_scan:
        admin_tasks["library_scan"]["message"] = f"扫描 {category}/{album}..."

        # 如果图集已存在但不完整，先删除旧记录
        if (category, album) in existing_albums:
            c.execute('DELETE FROM images WHERE category = ? AND album = ?', (category, album))

        for file in files:
            file_path = os.path.join(base_path, category, album, file)

            # 使用与普通扫描一致的路径格式
            import urllib.parse
            try:
                encoded_category = urllib.parse.quote(category, safe='')
                encoded_album = urllib.parse.quote(album, safe='')
                encoded_file = urllib.parse.quote(file, safe='')
                web_path = f'/images/{encoded_category}/{encoded_album}/{encoded_file}'
            except Exception as e:
                print(f"URL编码失败: {e}, 使用原始路径")
                web_path = f'/images/{category}/{album}/{file}'

            full_path = web_path

            try:
                file_size = os.path.getsize(file_path)
            except:
                file_size = 0

            c.execute('''INSERT INTO images
                         (filename, path, full_path, category, album, file_size)
                         VALUES (?, ?, ?, ?, ?, ?)''',
                     (file, web_path, full_path, category, album, file_size))

            processed += 1
            admin_tasks["library_scan"]["current"] = processed
            admin_tasks["library_scan"]["progress"] = int(100 * processed / total_files)

            # 每100个文件提交一次
            if processed % 100 == 0:
                conn.commit()

    # 最终提交
    conn.commit()
    conn.close()

    admin_tasks["library_scan"]["message"] = "扫描完成！"
    admin_tasks["library_scan"]["logs"].append(f"扫描完成！新增/更新 {processed} 个图片文件")

def scan_and_store_images():
    """增量扫描图片并存储到数据库"""
    global scan_progress

    base_path = 'downloaded'
    categories = ['korea', 'cosplay', 'japan', 'gravure', 'chinese', 'thailand']
    
    print("\n🌸 开始增量扫描图片文件...")
    
    # 获取已存在的图集
    existing_albums = get_existing_albums()
    print(f"📊 数据库中已有 {len(existing_albums)} 个图集")
    
    # 统计需要扫描的文件
    scan_progress['status'] = '分析需要扫描的内容...'
    scan_progress['current'] = 0
    scan_progress['total'] = 0
    
    albums_to_scan = []
    total_files = 0
    
    for category in categories:
        category_path = os.path.join(base_path, category)
        if os.path.exists(category_path):
            for album in os.listdir(category_path):
                album_path = os.path.join(category_path, album)
                if os.path.isdir(album_path):
                    # 检查是否需要扫描这个图集
                    need_scan = False
                    
                    if (category, album) in existing_albums:
                        # 已存在，检查完整性
                        if not check_album_integrity(category, album, base_path):
                            print(f"⚠️  图集 {category}/{album} 不完整，需要重新扫描")
                            need_scan = True
                    else:
                        # 不存在，需要扫描
                        need_scan = True
                    
                    if need_scan:
                        album_files = []
                        for file in os.listdir(album_path):
                            if file.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.webp')):
                                album_files.append(file)
                        
                        if album_files:
                            albums_to_scan.append((category, album, album_files))
                            total_files += len(album_files)
    
    scan_progress['total'] = total_files
    
    if total_files == 0:
        print("✅ 所有图集都已是最新状态，无需扫描")
        scan_progress['status'] = '所有内容都是最新的'
        scan_progress['completed'] = True
        return
    
    print(f"📊 需要扫描 {len(albums_to_scan)} 个图集，共 {total_files} 个文件")
    
    conn = sqlite3.connect('gallery.db')
    c = conn.cursor()
    
    processed = 0
    
    # 扫描需要更新的图集
    for category, album, files in albums_to_scan:
        scan_progress['status'] = f'扫描 {category}/{album}...'
        
        # 如果图集已存在但不完整，先删除旧记录
        if (category, album) in existing_albums:
            c.execute('DELETE FROM images WHERE category = ? AND album = ?', (category, album))
        
        album_path = os.path.join(base_path, category, album)
        
        for file in files:
            full_path = os.path.join(album_path, file)
            # BUG FIX: 正确处理URL编码，确保中文和特殊字符能正确访问
            import urllib.parse
            try:
                encoded_category = urllib.parse.quote(category, safe='')
                encoded_album = urllib.parse.quote(album, safe='')
                encoded_file = urllib.parse.quote(file, safe='')
                web_path = f'/images/{encoded_category}/{encoded_album}/{encoded_file}'
            except Exception as e:
                print(f"URL编码失败: {e}, 使用原始路径")
                web_path = f'/images/{category}/{album}/{file}'

            # 获取文件大小
            try:
                file_size = os.path.getsize(full_path)
            except:
                file_size = 0

            # 存储到数据库
            c.execute('''INSERT INTO images
                         (filename, path, full_path, category, album, file_size)
                         VALUES (?, ?, ?, ?, ?, ?)''',
                     (file, web_path, full_path, category, album, file_size))
            
            processed += 1
            scan_progress['current'] = processed
            
            # 更新进度条
            print_progress_bar(processed, total_files, f'扫描 {category}/{album}')
            
            # 每100个文件提交一次
            if processed % 100 == 0:
                conn.commit()
    
    # 最终提交
    conn.commit()
    conn.close()
    
    scan_progress['status'] = '扫描完成！'
    scan_progress['completed'] = True
    
    print(f"\n✅ 增量扫描完成！新增/更新 {processed} 个图片文件")
    print("📁 最新分类统计:")
    
    # 显示统计信息
    conn = sqlite3.connect('gallery.db')
    c = conn.cursor()
    for category in categories:
        c.execute('SELECT COUNT(*) FROM images WHERE category = ?', (category,))
        count = c.fetchone()[0]
        c.execute('SELECT COUNT(DISTINCT album) FROM images WHERE category = ?', (category,))
        albums = c.fetchone()[0]
        if count > 0:
            print(f"   🌸 {get_category_name(category)}: {albums}个图集, {count}张图片")
    conn.close()

def get_category_name(category):
    """获取分类中文名"""
    names = {
        'korea': '韩系风格',
        'cosplay': '角色扮演',
        'japan': '日系清新',
        'gravure': '专业写真',
        'chinese': '中式古典',
        'thailand': '泰式风情'
    }
    return names.get(category, category)

# 登录验证装饰器
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

# 从数据库获取图片数据
def get_images_from_db(category=None, album=None, search=None, limit=None, offset=None):
    conn = sqlite3.connect('gallery.db')
    c = conn.cursor()
    
    query = 'SELECT id, filename, path, full_path, category, album, file_size, created_at FROM images WHERE 1=1'
    params = []
    
    if category:
        query += ' AND category = ?'
        params.append(category)
    
    if album:
        query += ' AND album = ?'
        params.append(album)
    
    if search:
        query += ' AND (album LIKE ? OR filename LIKE ?)'
        params.extend([f'%{search}%', f'%{search}%'])
    
    query += ' ORDER BY created_at DESC'
    
    if limit:
        query += ' LIMIT ?'
        params.append(limit)
    
    if offset:
        query += ' OFFSET ?'
        params.append(offset)
    
    c.execute(query, params)
    results = c.fetchall()
    conn.close()
    
    images = []
    for row in results:
        images.append({
            'id': row[0],
            'filename': row[1],
            'path': row[2],
            'full_path': row[3],
            'category': row[4],
            'album': row[5],
            'file_size': row[6],
            'created_at': row[7]
        })
    
    return images

# 获取图集列表
def get_albums_from_db(category=None):
    conn = sqlite3.connect('gallery.db')
    c = conn.cursor()
    
    if category:
        query = '''SELECT category, album, COUNT(*) as image_count,
                           MIN(path) as cover_image, MIN(created_at) as created_at
                   FROM images 
                   WHERE category = ?
                   GROUP BY category, album 
                   ORDER BY created_at DESC'''
        c.execute(query, (category,))
    else:
        query = '''SELECT category, album, COUNT(*) as image_count,
                           MIN(path) as cover_image, MIN(created_at) as created_at
                   FROM images 
                   GROUP BY category, album 
                   ORDER BY created_at DESC'''
        c.execute(query)
    
    results = c.fetchall()
    conn.close()
    
    albums = []
    for row in results:
        albums.append({
            'category': row[0],
            'album': row[1],
            'image_count': row[2],
            'cover_image': row[3],
            'created_at': row[4]
        })
    
    return albums

# BUG FIX: 重构此函数以支持高效的过滤、排序和分页
def get_user_favorites(user_id, category=None, sort='newest', limit=None, offset=None):
    """
    获取用户收藏的图片，支持过滤、排序和分页
    """
    conn = sqlite3.connect('gallery.db')
    c = conn.cursor()
    
    base_query = '''FROM favorites f 
                    JOIN images i ON f.image_id = i.id 
                    WHERE f.user_id = ?'''
    params = [user_id]
    
    # 添加分类过滤
    if category:
        base_query += ' AND i.category = ?'
        params.append(category)

    # 获取总数
    count_query = f'SELECT COUNT(f.id) {base_query}'
    c.execute(count_query, params)
    total_count = c.fetchone()[0]

    # 构建主查询
    query = f'''SELECT i.id, i.filename, i.path, i.full_path, i.category, i.album, i.file_size, f.created_at as favorited_at
                {base_query}'''

    # 添加排序
    sort_map = {
        'newest': 'f.created_at DESC',
        'oldest': 'f.created_at ASC',
        'album': 'i.album ASC, i.filename ASC'
    }
    order_by_clause = sort_map.get(sort, 'f.created_at DESC')
    query += f' ORDER BY {order_by_clause}'

    # 添加分页
    if limit is not None:
        query += ' LIMIT ?'
        params.append(limit)
    
    if offset is not None:
        query += ' OFFSET ?'
        params.append(offset)
    
    c.execute(query, params)
    results = c.fetchall()
    conn.close()
    
    favorites = []
    for row in results:
        favorites.append({
            'id': row[0],
            'filename': row[1],
            'path': row[2],
            'full_path': row[3],
            'category': row[4],
            'album': row[5],
            'file_size': row[6],
            'favorited_at': row[7]
        })
    
    return favorites, total_count

def get_user_favorite_images(user_id, category=None, sort='newest', limit=12, offset=0):
    """获取用户收藏的图片（平铺显示）"""
    conn = sqlite3.connect('gallery.db')
    c = conn.cursor()

    try:
        # 构建查询条件
        where_conditions = ['f.user_id = ?']
        params = [user_id]

        if category:
            where_conditions.append('i.category = ?')
            params.append(category)

        where_clause = ' AND '.join(where_conditions)

        # 排序条件
        if sort == 'oldest':
            order_clause = 'f.created_at ASC'
        else:  # newest
            order_clause = 'f.created_at DESC'

        # 查询总数
        count_query = f'''
            SELECT COUNT(*)
            FROM favorites f
            JOIN images i ON f.image_id = i.id
            WHERE {where_clause}
        '''
        c.execute(count_query, params)
        total_count = c.fetchone()[0]

        # 查询数据
        query = f'''
            SELECT i.id, i.filename, i.path, i.full_path, i.category, i.album,
                   i.file_size, i.created_at, f.created_at as favorited_at
            FROM favorites f
            JOIN images i ON f.image_id = i.id
            WHERE {where_clause}
            ORDER BY {order_clause}
            LIMIT ? OFFSET ?
        '''

        c.execute(query, params + [limit, offset])
        rows = c.fetchall()

        favorites = []
        for row in rows:
            favorites.append({
                'id': row[0],
                'filename': row[1],
                'path': row[2],
                'full_path': row[3],
                'category': row[4],
                'album': row[5],
                'file_size': row[6],
                'created_at': row[7],
                'favorited_at': row[8]
            })

        return favorites, total_count

    finally:
        conn.close()

def get_user_favorite_images_grouped(user_id, category=None, sort='newest', limit=12, offset=0):
    """获取用户收藏的图片（按图集分组）"""
    conn = sqlite3.connect('gallery.db')
    c = conn.cursor()

    try:
        # 构建查询条件
        where_conditions = ['f.user_id = ?']
        params = [user_id]

        if category:
            where_conditions.append('i.category = ?')
            params.append(category)

        where_clause = ' AND '.join(where_conditions)

        # 排序条件
        if sort == 'oldest':
            order_clause = 'MIN(f.created_at) ASC'
        else:  # newest
            order_clause = 'MIN(f.created_at) DESC'

        # 查询按图集分组的收藏
        query = f'''
            SELECT i.category, i.album, COUNT(*) as image_count,
                   MIN(f.created_at) as first_favorited
            FROM favorites f
            JOIN images i ON f.image_id = i.id
            WHERE {where_clause}
            GROUP BY i.category, i.album
            ORDER BY {order_clause}
            LIMIT ? OFFSET ?
        '''

        c.execute(query, params + [limit, offset])
        album_rows = c.fetchall()

        # 获取总的图集数量
        count_query = f'''
            SELECT COUNT(DISTINCT i.category || '|' || i.album)
            FROM favorites f
            JOIN images i ON f.image_id = i.id
            WHERE {where_clause}
        '''
        c.execute(count_query, params)
        total_count = c.fetchone()[0]

        groups = []
        for album_row in album_rows:
            category_name, album_name, image_count, first_favorited = album_row

            # 获取该图集的收藏图片
            images_query = f'''
                SELECT i.id, i.filename, i.path, i.full_path, i.file_size,
                       i.created_at, f.created_at as favorited_at
                FROM favorites f
                JOIN images i ON f.image_id = i.id
                WHERE f.user_id = ? AND i.category = ? AND i.album = ?
                ORDER BY f.created_at DESC
            '''

            c.execute(images_query, [user_id, category_name, album_name])
            image_rows = c.fetchall()

            images = []
            for img_row in image_rows:
                images.append({
                    'id': img_row[0],
                    'filename': img_row[1],
                    'path': img_row[2],
                    'full_path': img_row[3],
                    'file_size': img_row[4],
                    'created_at': img_row[5],
                    'favorited_at': img_row[6]
                })

            groups.append({
                'category': category_name,
                'album': album_name,
                'image_count': image_count,
                'first_favorited': first_favorited,
                'images': images
            })

        return groups, total_count

    finally:
        conn.close()

# 图集详情页面模板 - 修复后的版本
ALBUM_TEMPLATE = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ album }} - {{ category_name }} - 春色写真馆</title>
    <link rel="icon" type="image/svg+xml" href="/favicon.ico">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* 移除所有列表标记 */
        ul, ol { list-style: none; }
        li::marker { display: none; }

        body {
            font-family: 'PingFang SC', 'Hiragino Sans GB', 'Noto Sans CJK SC', 'Source Han Sans SC', 'Microsoft YaHei', '微软雅黑', sans-serif;
            background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 25%, #f0fff4 50%, #fff8dc 75%, #fdf5e6 100%);
            min-height: 100vh;
            color: #5d4e37;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            padding: 1rem 0;
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
            box-shadow: 0 2px 20px rgba(255, 182, 193, 0.3);
        }
        
        .nav {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
        }
        
        .logo {
            font-size: 2rem;
            font-weight: bold;
            background: linear-gradient(45deg, #ff69b4, #ffa500, #98fb98);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        .nav-links {
            display: flex;
            gap: 2rem;
            align-items: center;
        }
        
        .nav-link {
            color: #8b4513;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .nav-link:hover {
            background: linear-gradient(45deg, rgba(255, 182, 193, 0.3), rgba(255, 218, 185, 0.3));
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 182, 193, 0.4);
        }
        
        .user-info {
            display: flex;
            align-items: center;
            justify-content: center; /* 水平居中 */
            gap: 0.3rem;
            max-width: 120px; /* 减小整体宽度 */
            flex-shrink: 0; /* 防止压缩 */
            position: relative;
        }

        .user-info .avatar {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            flex-shrink: 0; /* 头像不压缩 */
        }

        .user-info span {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 50px; /* 进一步减小昵称宽度 */
            font-size: 0.8rem;
            cursor: pointer;
            position: relative;
            display: inline-block;
            vertical-align: middle;
            flex-shrink: 1; /* 允许文字压缩 */
            min-width: 0; /* 允许收缩到0 */
        }

        /* User dropdown menu - 重新设计 */
        .user-dropdown {
            position: relative;
            display: inline-block;
        }

        .user-dropdown-content {
            display: none;
            position: absolute;
            right: 0;
            top: 100%;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            min-width: 150px;
            box-shadow: 0 8px 25px rgba(255, 182, 193, 0.3);
            border-radius: 10px;
            z-index: 1001;
            border: 1px solid rgba(255, 182, 193, 0.2);
            overflow: hidden;
            margin-top: 5px;
        }

        .user-dropdown-content.show {
            display: block;
        }

        .dropdown-item {
            display: block;
            padding: 0.8rem 1rem;
            color: #8b4513;
            text-decoration: none;
            transition: all 0.3s ease;
            border-bottom: 1px solid rgba(255, 182, 193, 0.1);
        }

        .dropdown-item:last-child {
            border-bottom: none;
        }

        .dropdown-item:hover {
            background: linear-gradient(45deg, #ff69b4, #ffa500);
            color: white;
            transform: translateX(5px);
        }

        /* 移动端模态框样式下拉菜单 */
        .mobile-dropdown-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 9999;
            justify-content: center;
            align-items: center;
        }

        .mobile-dropdown-modal.show {
            display: flex;
        }

        .mobile-dropdown-content {
            background: white;
            border-radius: 15px;
            padding: 1rem;
            min-width: 200px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            animation: slideUp 0.3s ease;
        }

        @keyframes slideUp {
            from {
                transform: translateY(50px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .mobile-dropdown-item {
            display: block;
            padding: 1rem;
            color: #8b4513;
            text-decoration: none;
            text-align: center;
            border-bottom: 1px solid rgba(255, 182, 193, 0.2);
            transition: all 0.3s ease;
        }

        .mobile-dropdown-item:last-child {
            border-bottom: none;
        }

        .mobile-dropdown-item:hover {
            background: linear-gradient(45deg, #ff69b4, #ffa500);
            color: white;
        }
        
        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 2px solid #ffb6c1;
            object-fit: cover;
        }
        
        .main-content {
            margin-top: 100px;
            padding: 2rem;
            max-width: 1400px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .breadcrumb {
            background: rgba(255, 255, 255, 0.8);
            padding: 1rem 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            font-size: 0.9rem;
        }
        
        .breadcrumb a {
            color: #8b4513;
            text-decoration: none;
            transition: color 0.3s ease;
        }
        
        .breadcrumb a:hover {
            color: #ff69b4;
        }
        
        .album-header {
            background: linear-gradient(135deg, rgba(255, 182, 193, 0.1), rgba(255, 218, 185, 0.1));
            padding: 3rem 2rem;
            border-radius: 30px;
            margin-bottom: 3rem;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .album-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 182, 193, 0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        
        .album-title {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #ff69b4, #ffa500, #98fb98, #dda0dd);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            position: relative;
            z-index: 1;
        }
        
        .album-info {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 1.5rem;
            margin-top: 1.5rem;
            position: relative;
            z-index: 1;
            flex-wrap: wrap;
        }
        
        .info-item {
            background: rgba(255, 255, 255, 0.8);
            padding: 0.6rem 1.2rem;
            border-radius: 15px;
            font-size: 0.9rem;
            color: #8b4513;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 2.5rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(5px);
        }

        .album-favorite-btn {
            background: linear-gradient(45deg, #ff69b4, #ffa500);
            color: white;
            border: none;
            padding: 0.6rem 1.2rem;
            border-radius: 15px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(255, 105, 180, 0.3);
            min-height: 2.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .album-favorite-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 105, 180, 0.4);
        }

        .album-favorite-btn.favorited {
            background: linear-gradient(45deg, #ff1493, #ff6347);
        }
        
        .gallery-controls {
            background: rgba(255, 255, 255, 0.9);
            padding: 1rem 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }
        
        .view-controls {
            display: flex;
            gap: 1rem;
            align-items: center;
        }
        
        .view-btn {
            padding: 0.5rem 1rem;
            border: 1px solid #ffb6c1;
            background: rgba(255, 255, 255, 0.9);
            color: #8b4513;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .view-btn.active, .view-btn:hover {
            background: linear-gradient(45deg, #ff69b4, #ffa500);
            color: white;
            transform: translateY(-2px);
        }
        
        .batch-actions {
            display: flex;
            gap: 1rem;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #ff69b4, #ffa500);
            color: white;
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #98fb98, #87ceeb);
            color: #2f4f4f;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 105, 180, 0.4);
        }
        
        .gallery-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }
        
        .gallery-grid.large {
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 2rem;
        }
        
        .gallery-grid.small {
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 1rem;
        }
        
        .image-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            overflow: hidden;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            box-shadow: 0 4px 15px rgba(255, 182, 193, 0.2);
        }
        
        .image-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(255, 182, 193, 0.4);
        }
        
        .image-card img {
            width: 100%;
            height: 200px;
            object-fit: cover;
            transition: transform 0.3s ease;
        }
        
        .gallery-grid.large .image-card img {
            height: 250px;
        }
        
        .gallery-grid.small .image-card img {
            height: 150px;
        }
        
        .image-card:hover img {
            transform: scale(1.05);
        }
        
        .image-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.7) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
            display: flex;
            align-items: flex-end;
            padding: 1rem;
        }
        
        .image-card:hover .image-overlay {
            opacity: 1;
        }
        
        .image-actions {
            display: flex;
            gap: 0.5rem;
        }
        
        .action-btn {
            background: rgba(255, 255, 255, 0.9);
            border: none;
            padding: 0.5rem;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .action-btn:hover {
            background: #ff69b4;
            color: white;
            transform: scale(1.1);
        }
        
        .action-btn.favorited {
            background: #ff69b4;
            color: white;
        }
        
        .loading {
            text-align: center;
            padding: 2rem;
            font-size: 1.2rem;
            color: #8b4513;
        }
        
        .load-more {
            text-align: center;
            margin: 2rem 0;
        }
        
        .load-more-btn {
            padding: 1rem 2rem;
            background: linear-gradient(45deg, #ff69b4, #ffa500);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .load-more-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 105, 180, 0.4);
        }
        
        .load-more-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        /* 图片查看器模态框 */
        .modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.95);
            backdrop-filter: blur(5px);
        }
        
        .modal-content {
            position: relative;
            margin: 0 auto;
            max-width: 95%;
            max-height: 95%;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100vh;
            padding: 2% 0;
        }
        
        .modal-image {
            max-width: 100%;
            max-height: 90vh;
            object-fit: contain;
            border-radius: 10px;
        }
        
        .modal-nav {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.9);
            border: none;
            padding: 1rem;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.5rem;
            transition: all 0.3s ease;
            z-index: 2001;
        }
        
        .modal-nav:hover {
            background: #ff69b4;
            color: white;
            transform: translateY(-50%) scale(1.1);
        }
        
        .modal-prev {
            left: 20px;
        }
        
        .modal-next {
            right: 20px;
        }
        
        .modal-close {
            position: absolute;
            top: 20px;
            right: 30px;
            color: #fff;
            font-size: 40px;
            font-weight: bold;
            cursor: pointer;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 50%;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            z-index: 2001;
        }
        
        .modal-close:hover {
            background: rgba(255, 105, 180, 0.8);
            transform: scale(1.1);
        }
        
        .modal-info {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 1rem 2rem;
            border-radius: 25px;
            text-align: center;
            z-index: 2001;
        }

        .modal-favorite {
            position: absolute;
            bottom: 20px;
            right: 100px; /* Position next to close button */
            width: 60px;
            height: 60px;
            font-size: 28px;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
            border: none;
            border-radius: 50%;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            z-index: 2001;
        }

        .modal-favorite:hover {
            background: rgba(255, 105, 180, 0.8);
            transform: scale(1.1);
        }

        .modal-favorite.favorited {
            background: rgba(255, 105, 180, 0.8);
            color: #ff1493;
        }
        
        /* 手机端适配 - 修复后的版本 */
        @media (max-width: 768px) {
            .header {
                padding: 0.5rem 0;
                position: fixed;
                top: 0;
                width: 100%;
                z-index: 1000;
            }

            .nav {
                flex-direction: row;
                gap: 0.5rem;
                padding: 0 0.5rem;
                align-items: center;
                justify-content: space-between;
            }

            .logo {
                font-size: 1.2rem;
                flex-shrink: 0;
            }

            .nav-links {
                gap: 0.3rem;
                flex-wrap: nowrap;
                justify-content: flex-end;
                align-items: center;
                overflow-x: auto;
                flex: 1;
                min-width: 0;
            }

            .nav-link {
                font-size: 0.8rem;
                padding: 0.3rem 0.5rem;
                white-space: nowrap;
                flex-shrink: 0;
            }

            .user-info {
                flex-direction: row;
                gap: 0.2rem;
                align-items: center;
                justify-content: center; /* 移动端也居中 */
                max-width: 80px; /* 进一步减小移动端宽度 */
                flex-shrink: 0;
            }

            .user-info .avatar {
                width: 20px;
                height: 20px;
                flex-shrink: 0; /* 头像不压缩 */
            }

            .user-info span {
                max-width: 35px; /* 大幅减小移动端文字宽度 */
                font-size: 0.65rem;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                display: inline-block;
                vertical-align: middle;
                flex-shrink: 1; /* 允许文字压缩 */
                min-width: 0; /* 允许收缩到0 */
            }

            /* 移动端隐藏桌面版下拉菜单，使用模态框版本 */
            .user-dropdown-content {
                display: none !important;
            }
            
            .main-content {
                margin-top: 140px; /* 增加顶部边距避免重叠 */
                padding: 1rem;
            }
            
            .album-title {
                font-size: 2rem;
            }
            
            .album-info {
                flex-direction: column;
                gap: 1rem;
            }
            
            .gallery-controls {
                flex-direction: column;
                align-items: stretch;
            }
            
            .view-controls, .batch-actions {
                justify-content: center;
            }
            
            .gallery-grid {
                grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
                gap: 1rem;
            }
            
            /* 手机端模态框样式 - 修复版本 */
            .modal-content {
                height: 100vh;
                padding: 5% 0;
                align-items: center;
                justify-content: center;
            }
            
            .modal-image {
                max-width: 95%;
                max-height: 85vh;
                object-fit: contain;
                border-radius: 10px;
            }
            
            /* 手机端模态框导航按钮 - 修复版本 */
            .modal-nav {
                padding: 0.8rem;
                font-size: 1.2rem;
                width: 50px;
                height: 50px;
                display: flex;
                align-items: center;
                justify-content: center;
                background: rgba(255, 255, 255, 0.9);
                backdrop-filter: blur(5px);
            }
            
            .modal-prev {
                left: 10px;
            }
            
            .modal-next {
                right: 10px;
            }
            
            .modal-close {
                top: auto;
                bottom: 20px;
                right: 15px;
                width: 50px;
                height: 50px;
                font-size: 30px;
                background: rgba(0, 0, 0, 0.7);
                backdrop-filter: blur(5px);
            }

            .modal-favorite {
                position: absolute;
                bottom: 20px;
                right: 75px; /* Position next to close button */
                width: 50px;
                height: 50px;
                font-size: 24px;
                background: rgba(0, 0, 0, 0.7);
                backdrop-filter: blur(5px);
                border: none;
                border-radius: 50%;
                color: white;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.3s ease;
                z-index: 2001;
            }

            .modal-favorite:hover {
                background: rgba(255, 105, 180, 0.8);
                transform: scale(1.1);
            }

            .modal-favorite.favorited {
                background: rgba(255, 105, 180, 0.8);
                color: #ff1493;
            }
            
            .modal-info {
                bottom: 10px;
                padding: 0.8rem 1.5rem;
                font-size: 0.9rem;
                background: rgba(0, 0, 0, 0.8);
                backdrop-filter: blur(5px);
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <nav class="nav">
            <a href="/" class="logo" style="text-decoration: none; color: inherit;">🌸 春色写真馆</a>
            <div class="nav-links">
                <a href="/" class="nav-link">首页</a>
                <a href="/gallery" class="nav-link">图库</a>
                <a href="/favorites" class="nav-link">收藏</a>
                {% if session.user_id %}
                    <a href="/admin" class="nav-link">管理</a>
                    <div class="user-info user-dropdown">
                        <img src="{{ session.avatar or default_avatar }}" alt="头像" class="avatar">
                        <span onclick="toggleUserDropdown()" title="点击查看选项">{{ session.username }}</span>
                        <div class="user-dropdown-content" id="userDropdown">
                            <a href="/change-password" class="dropdown-item">修改密码</a>
                            <a href="/logout" class="dropdown-item">退出</a>
                        </div>
                    </div>
                {% else %}
                    <a href="/login" class="nav-link">登录</a>
                    <a href="/register" class="nav-link">注册</a>
                {% endif %}
            </div>
        </nav>
    </header>

    <main class="main-content">
        <div class="breadcrumb">
            <a href="/">首页</a> > 
            <a href="/?category={{ category }}">{{ category_name }}</a> > 
            <span>{{ album }}</span>
        </div>

        <div class="album-header">
            {% if favorite_only %}
            <h1 class="album-title">💖 {{ album }} (收藏)</h1>
            <div class="album-info">
                <div class="info-item">📂 {{ category_name }}</div>
                <div class="info-item">📸 <span id="imageCount">加载中...</span> 张收藏图片</div>
                <div class="info-item">📅 <span id="createDate">加载中...</span></div>
                <div class="info-item">
                    <a href="/favorites" class="btn btn-secondary">🔙 返回收藏页面</a>
                </div>
            </div>
            {% else %}
            <h1 class="album-title">{{ album }}</h1>
            <div class="album-info">
                <div class="info-item">📂 {{ category_name }}</div>
                <div class="info-item">📸 <span id="imageCount">加载中...</span> 张图片</div>
                <div class="info-item">📅 <span id="createDate">加载中...</span></div>
                {% if session.user_id %}
                <div class="info-item">
                    <button id="albumFavoriteBtn" class="album-favorite-btn" onclick="toggleAlbumFavorite()" title="收藏图集">
                        ❤️ 收藏图集
                    </button>
                </div>
                {% endif %}
            </div>
            {% endif %}
        </div>

        <div class="gallery-controls">
            <div class="view-controls">
                <button class="view-btn active" onclick="setViewSize('medium')" data-size="medium">中等</button>
                <button class="view-btn" onclick="setViewSize('large')" data-size="large">大图</button>
                <button class="view-btn" onclick="setViewSize('small')" data-size="small">小图</button>
            </div>
            <div class="batch-actions">
                {% if session.user_id %}
                    {% if favorite_only %}
                    <!-- 收藏模式：显示查看原图集和取消收藏全部 -->
                    <button class="btn btn-secondary" onclick="viewOriginalAlbum()">📂 查看原图集</button>
                    <button class="btn btn-danger" onclick="unfavoriteAll()">💔 取消收藏全部</button>
                    {% else %}
                    <!-- 普通模式：显示收藏全部 -->
                    <button class="btn btn-primary" onclick="favoriteAll()">❤️ 收藏全部</button>
                    {% endif %}
                {% endif %}
                <button class="btn btn-secondary" onclick="downloadAll()">⬇️ 下载全部</button>
            </div>
        </div>

        <div class="gallery-grid" id="galleryGrid">
            <div class="loading">正在加载图片...</div>
        </div>

        <div class="load-more" id="loadMoreContainer" style="display: none;">
            <button class="load-more-btn" id="loadMoreBtn" onclick="loadMoreImages()">加载更多</button>
        </div>
    </main>

    <!-- 图片查看器模态框 -->
    <div id="imageModal" class="modal">
        <div class="modal-content">
            <span class="modal-close" onclick="closeModal()">&times;</span>
            {% if session.user_id %}
            <button class="modal-favorite" id="modalFavorite" onclick="toggleModalFavorite()" title="收藏">❤️</button>
            {% endif %}
            <button class="modal-nav modal-prev" onclick="prevImage()">‹</button>
            <img class="modal-image" id="modalImage" src="{{ placeholder_svg }}" alt="">
            <button class="modal-nav modal-next" onclick="nextImage()">›</button>
            <div class="modal-info">
                <div id="modalInfo">1 / 10</div>
            </div>
        </div>
    </div>

    <!-- 移动端用户下拉菜单模态框 -->
    <div class="mobile-dropdown-modal" id="mobileDropdownModal">
        <div class="mobile-dropdown-content">
            <a href="/change-password" class="mobile-dropdown-item">修改密码</a>
            <a href="/logout" class="mobile-dropdown-item">退出</a>
        </div>
    </div>

    <script>
        let currentImages = [];
        let currentPage = 1;
        let itemsPerPage = 20;
        let loading = false;
        let hasMore = true;
        let currentModalIndex = 0;
        let userFavorites = new Set(); // 存储用户收藏的图片ID
        const placeholderSvg = '{{ placeholder_svg }}';

        // 统一懒加载系统
        let allImages = []; // 存储所有图片数据
        let modalLoadedImages = new Set(); // 跟踪已加载的图片索引
        let modalLoadingBatch = false; // 防止重复加载
        let totalImagesCount = 0; // 总图片数量
        const MODAL_BATCH_SIZE = 30; // 每批加载30张图片
        const MODAL_PRELOAD_THRESHOLD = 10; // 距离末尾10张时开始预加载
        const MODAL_SMART_PRELOAD_RATIO = 0.75; // 当阅读进度达到75%时开始智能预加载
        const MODAL_BOUNDARY_PRELOAD_THRESHOLD = 8; // 距离已加载边界8张时开始预加载

        // 统一懒加载管理器
        const LazyLoadManager = {
            isGalleryLoading: false,
            isModalLoading: false,
            lastGalleryLoadTime: 0,
            lastModalLoadTime: 0,
            loadCooldown: 1000, // 1秒冷却时间，防止重复触发
            galleryLoadTimeout: null,
            modalLoadTimeout: null,

            // 检查是否可以触发加载
            canTriggerLoad() {
                const now = Date.now();
                return (now - this.lastGalleryLoadTime > this.loadCooldown) &&
                       (now - this.lastModalLoadTime > this.loadCooldown);
            },

            // 强制重置状态（用于故障恢复）
            forceReset() {
                console.log(`🔄 Force resetting LazyLoadManager state`);
                this.isGalleryLoading = false;
                this.isModalLoading = false;
                if (this.galleryLoadTimeout) {
                    clearTimeout(this.galleryLoadTimeout);
                    this.galleryLoadTimeout = null;
                }
                if (this.modalLoadTimeout) {
                    clearTimeout(this.modalLoadTimeout);
                    this.modalLoadTimeout = null;
                }
            },

            // 统一触发懒加载
            async triggerUnifiedLazyLoad(reason, context = {}) {
                // 检查是否有卡死的状态，如果有则强制重置
                const now = Date.now();
                const galleryStuckTime = this.isGalleryLoading && (now - this.lastGalleryLoadTime > 10000); // 10秒
                const modalStuckTime = this.isModalLoading && (now - this.lastModalLoadTime > 10000); // 10秒

                if (galleryStuckTime || modalStuckTime) {
                    console.log(`⚠️ Detected stuck loading state, force resetting...`);
                    this.forceReset();
                }

                if (!this.canTriggerLoad()) {
                    console.log(`🚫 Lazy load blocked by cooldown (${reason})`);
                    return;
                }

                console.log(`🚀 UNIFIED LAZY LOAD TRIGGERED: ${reason}`, context);

                // 并行启动两套加载系统
                const promises = [];

                // 1. 图集页面懒加载（优先级最高）
                if (hasMore && !loading && !this.isGalleryLoading) {
                    console.log(`📄 Starting gallery lazy load...`);
                    this.lastGalleryLoadTime = Date.now();
                    promises.push(this.loadGalleryData());
                } else {
                    console.log(`📄 Gallery load skipped: hasMore=${hasMore}, loading=${loading}, isGalleryLoading=${this.isGalleryLoading}`);
                }

                // 2. 模态框懒加载（仅当allImages可用时）
                if (allImages.length > 0 && !modalLoadingBatch && !this.isModalLoading) {
                    console.log(`🖼️ Starting modal lazy load...`);
                    this.lastModalLoadTime = Date.now();
                    promises.push(this.loadModalData(context.currentIndex || currentModalIndex));
                } else {
                    console.log(`🖼️ Modal load skipped: allImages.length=${allImages.length}, modalLoadingBatch=${modalLoadingBatch}, isModalLoading=${this.isModalLoading}`);
                }

                // 等待所有加载完成
                try {
                    await Promise.all(promises);
                    console.log(`✅ Unified lazy load completed`);
                } catch (error) {
                    console.error(`❌ Unified lazy load failed:`, error);
                } finally {
                    this.isGalleryLoading = false;
                    this.isModalLoading = false;
                }
            },

            // 加载图集数据（简化版）
            async loadGalleryData() {
                console.log(`📄 Starting gallery data load...`);

                if (!hasMore) {
                    console.log(`❌ No more gallery data to load (hasMore=${hasMore})`);
                    return;
                }

                if (loading) {
                    console.log(`❌ Gallery already loading (loading=${loading})`);
                    return;
                }

                try {
                    this.isGalleryLoading = true;
                    const startTime = Date.now();

                    // 设置安全超时，防止状态卡死
                    this.galleryLoadTimeout = setTimeout(() => {
                        console.log(`⏰ Gallery load safety timeout after 3 seconds, force resetting`);
                        this.isGalleryLoading = false;
                        this.galleryLoadTimeout = null;
                    }, 3000);

                    console.log(`🚀 Calling loadMoreImages() directly...`);

                    // 直接调用loadMoreImages，不使用复杂的Promise包装
                    const originalCallback = window.loadImagesCallback;

                    // 设置完成回调
                    window.loadImagesCallback = () => {
                        if (this.galleryLoadTimeout) {
                            clearTimeout(this.galleryLoadTimeout);
                            this.galleryLoadTimeout = null;
                        }
                        const duration = Date.now() - startTime;
                        console.log(`✅ Gallery load completed in ${duration}ms`);
                        this.isGalleryLoading = false;

                        // 恢复原始回调
                        if (originalCallback) {
                            originalCallback();
                        }
                    };

                    // 直接调用
                    loadMoreImages();

                } catch (error) {
                    console.error('❌ Gallery load failed:', error);
                    this.isGalleryLoading = false;
                    if (this.galleryLoadTimeout) {
                        clearTimeout(this.galleryLoadTimeout);
                        this.galleryLoadTimeout = null;
                    }
                }
            },

            // 加载模态框数据
            async loadModalData(currentIndex) {
                if (this.isModalLoading || modalLoadingBatch) {
                    console.log(`⏳ Modal loading already in progress, skipping`);
                    return;
                }

                try {
                    console.log(`🖼️ Starting modal data load for index ${currentIndex}...`);
                    const startTime = Date.now();

                    // 找到下一个需要加载的批次
                    let nextBatchStart = -1;
                    let totalUnloaded = 0;

                    for (let i = 0; i < totalImagesCount; i += MODAL_BATCH_SIZE) {
                        let batchLoaded = true;
                        let unloadedInBatch = 0;

                        for (let j = i; j < Math.min(i + MODAL_BATCH_SIZE, totalImagesCount); j++) {
                            if (!modalLoadedImages.has(j)) {
                                batchLoaded = false;
                                unloadedInBatch++;
                                totalUnloaded++;
                            }
                        }

                        if (!batchLoaded && nextBatchStart === -1) {
                            nextBatchStart = i;
                            console.log(`📦 Found next batch to load: ${i} (${unloadedInBatch} unloaded images)`);
                        }
                    }

                    console.log(`📊 Modal load analysis: ${totalUnloaded} total unloaded images, next batch: ${nextBatchStart}`);

                    if (nextBatchStart !== -1) {
                        await loadModalImageBatch(nextBatchStart);
                        const duration = Date.now() - startTime;
                        console.log(`✅ Modal batch load completed in ${duration}ms`);
                    } else {
                        console.log(`✅ All modal images already loaded`);
                    }
                } catch (error) {
                    console.error('❌ Modal load failed:', error);
                }
            }
        };

        // 全局调试函数（可在控制台调用）
        window.debugLazyLoad = {
            reset: () => LazyLoadManager.forceReset(),
            status: () => {
                console.log('=== LazyLoad Status ===');
                console.log(`isGalleryLoading: ${LazyLoadManager.isGalleryLoading}`);
                console.log(`isModalLoading: ${LazyLoadManager.isModalLoading}`);
                console.log(`hasMore: ${hasMore}`);
                console.log(`loading: ${loading}`);
                console.log(`modalLoadingBatch: ${modalLoadingBatch}`);
                console.log(`allImages.length: ${allImages.length}`);
                console.log(`currentImages.length: ${currentImages.length}`);
                console.log(`currentModalIndex: ${currentModalIndex}`);
                console.log(`currentPage: ${currentPage}`);
            },
            trigger: () => checkUnifiedLazyLoad(),
            testLoad: () => {
                console.log('🧪 Testing direct loadMoreImages call...');
                loadMoreImages();
            },
            forceLoad: () => {
                console.log('🔧 Force loading more images...');
                if (hasMore && !loading) {
                    currentPage++;
                    loadImages(true);
                } else {
                    console.log(`Cannot force load: hasMore=${hasMore}, loading=${loading}`);
                }
            }
        };

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', async function() {
            {% if session.user_id %}
            // 先加载用户收藏状态
            await loadUserFavorites();
            checkAlbumFavoriteStatus();
            {% endif %}

            // 然后加载基础图片（此时收藏状态已经加载完成）
            loadImages();

            // 等待基础图片加载完成后再加载元数据
            setTimeout(async () => {
                console.log(`🔄 Loading metadata after basic images loaded...`);
                await loadAllImagesMetadata(); // 加载所有图片元数据用于模态框

                // 元数据加载完成后，执行初始统一懒加载检查
                setTimeout(() => {
                    console.log(`🚀 Initial unified lazy load check after metadata loaded`);
                    checkUnifiedLazyLoad();
                }, 500); // 0.5秒后执行
            }, 1000); // 1秒后开始加载元数据
        });

        // 加载所有图片元数据（仅元数据，不加载实际图片）（修复版）
        async function loadAllImagesMetadata() {
            console.log(`=== loadAllImagesMetadata Debug ===`);
            try {
                const albumName = '{{ album }}';
                const category = '{{ category }}';
                const favoriteOnly = {{ 'true' if favorite_only else 'false' }};

                let url;
                if (favoriteOnly) {
                    // 收藏模式：获取收藏图片的元数据
                    url = `/api/album-favorites-only/${category}/${encodeURIComponent(albumName)}?page=1&limit=1000`;
                } else {
                    // 普通模式：获取所有图片的元数据
                    url = `/api/album/${category}/${encodeURIComponent(albumName)}/images?metadata_only=true`;
                }

                console.log(`Fetching metadata from: ${url}`);

                const response = await fetch(url);
                console.log(`Response status: ${response.status}`);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                console.log(`Received data:`, data);

                if (data.images && data.images.length > 0) {
                    allImages = data.images;
                    totalImagesCount = allImages.length;
                    console.log(`✅ Loaded metadata for ${totalImagesCount} images`);
                    console.log(`First few images:`, allImages.slice(0, 3));
                } else {
                    throw new Error('No images in response');
                }
            } catch (error) {
                console.error('❌ Failed to load images metadata:', error);
                console.log(`🔄 Using fallback: creating metadata from currentImages`);

                // 如果元数据加载失败，使用当前图片创建基础元数据
                if (currentImages.length > 0) {
                    allImages = [...currentImages]; // 复制当前图片
                    totalImagesCount = allImages.length;

                    // 标记当前图片为已加载
                    for (let i = 0; i < currentImages.length; i++) {
                        modalLoadedImages.add(i);
                    }

                    console.log(`📋 Fallback metadata created: ${totalImagesCount} images`);
                } else {
                    console.log(`⚠️ No currentImages available for fallback`);
                    allImages = [];
                    totalImagesCount = 0;
                }
            }
            console.log(`=== End loadAllImagesMetadata Debug ===`);
        }

        // 模态框批量加载图片
        async function loadModalImageBatch(startIndex) {
            if (modalLoadingBatch) return;
            modalLoadingBatch = true;

            try {
                const endIndex = Math.min(startIndex + MODAL_BATCH_SIZE, totalImagesCount);
                const albumName = '{{ album }}';
                const category = '{{ category }}';
                const favoriteOnly = {{ 'true' if favorite_only else 'false' }};

                let response;
                if (favoriteOnly) {
                    // 收藏模式：由于收藏图片数量通常较少，直接使用已加载的数据
                    // 不需要额外的批量加载，因为所有收藏图片已经在 allImages 中
                    console.log(`📊 Modal batch load skipped in favorite mode (using existing data)`);
                    modalLoadingBatch = false;
                    return;
                } else {
                    // 普通模式：批量加载
                    response = await fetch(`/api/album/${category}/${encodeURIComponent(albumName)}/images?start=${startIndex}&limit=${endIndex - startIndex}`);
                }

                const data = await response.json();

                if (data.images) {
                    // 将新加载的图片合并到allImages中
                    data.images.forEach((image, index) => {
                        const actualIndex = startIndex + index;
                        if (actualIndex < allImages.length) {
                            // 合并图片数据，保留元数据并添加完整数据
                            allImages[actualIndex] = {
                                ...allImages[actualIndex], // 保留原有元数据
                                ...image // 添加完整数据
                            };
                            modalLoadedImages.add(actualIndex);
                        }
                    });

                    console.log(`Loaded images batch: ${startIndex} to ${endIndex - 1} (${data.images.length} images)`);
                }
            } catch (error) {
                console.error('Failed to load modal image batch:', error);
            } finally {
                modalLoadingBatch = false;
            }
        }

        // 检查是否需要预加载更多图片 - 智能预加载版本
        function checkModalPreload(currentIndex) {
            if (modalLoadingBatch || totalImagesCount === 0) return;

            // 检查当前图片是否已加载完整数据
            const currentImage = allImages[currentIndex];
            if (!currentImage || !currentImage.file_size) {
                // 当前图片数据不完整，需要加载包含当前图片的批次
                const batchStart = Math.floor(currentIndex / MODAL_BATCH_SIZE) * MODAL_BATCH_SIZE;
                if (!modalLoadedImages.has(batchStart)) {
                    console.log(`Loading batch for current image at index ${currentIndex}`);
                    loadModalImageBatch(batchStart);
                    return;
                }
            }

            // 确保用户前方总是有足够的内容可以浏览
            ensureAdequatePreload(currentIndex);
        }

        // 确保用户前方有足够的内容可以浏览 - 智能预加载策略
        function ensureAdequatePreload(currentIndex) {
            // 找到从当前位置开始的连续已加载内容的边界
            let maxLoadedIndex = currentIndex - 1;
            for (let i = currentIndex; i < totalImagesCount; i++) {
                if (modalLoadedImages.has(i)) {
                    maxLoadedIndex = i;
                } else {
                    break;
                }
            }

            // 计算用户前方已加载的内容数量
            const preloadedAhead = maxLoadedIndex >= currentIndex ? maxLoadedIndex - currentIndex + 1 : 0;
            const totalProgress = (currentIndex + 1) / totalImagesCount;

            // 智能预加载策略：
            // 1. 如果用户前方预加载内容不足一个批次，立即加载
            // 2. 如果用户已经浏览了当前已加载内容的75%，开始预加载下一批
            // 3. 如果接近总内容的末尾，确保有足够内容
            const minPreloadAhead = Math.min(MODAL_BATCH_SIZE, totalImagesCount - currentIndex);
            const needImmediateLoad = preloadedAhead < minPreloadAhead && maxLoadedIndex < totalImagesCount - 1;

            // 计算在已加载内容中的进度
            const loadedContentProgress = preloadedAhead > 0 ?
                Math.min(1, (currentIndex - (maxLoadedIndex - preloadedAhead + 1) + 1) / preloadedAhead) : 1;
            const needSmartPreload = loadedContentProgress >= MODAL_SMART_PRELOAD_RATIO &&
                maxLoadedIndex < totalImagesCount - 1 && preloadedAhead >= MODAL_BOUNDARY_PRELOAD_THRESHOLD;

            if (needImmediateLoad || needSmartPreload) {
                // 找到下一个需要加载的批次
                const nextUnloadedIndex = maxLoadedIndex + 1;
                const nextBatchStart = Math.floor(nextUnloadedIndex / MODAL_BATCH_SIZE) * MODAL_BATCH_SIZE;

                const reason = needImmediateLoad ? 'insufficient preload' : 'smart preload trigger';
                console.log(`${reason}: current=${currentIndex + 1}/${totalImagesCount}, preloaded ahead=${preloadedAhead}, loaded progress=${(loadedContentProgress * 100).toFixed(1)}%, loading batch at ${nextBatchStart}`);
                loadModalImageBatch(nextBatchStart);
            }
        }

        // 检查图集收藏状态
        async function checkAlbumFavoriteStatus() {
            try {
                const response = await fetch(`/api/album-favorite-status?category={{ category }}&album={{ album }}`);
                const data = await response.json();
                updateAlbumFavoriteButton(data.is_favorited);
            } catch (error) {
                console.error('检查图集收藏状态失败:', error);
            }
        }

        // 更新图集收藏按钮状态
        function updateAlbumFavoriteButton(isFavorited) {
            const btn = document.getElementById('albumFavoriteBtn');
            if (btn) {
                if (isFavorited) {
                    btn.classList.add('favorited');
                    btn.innerHTML = '💖 已收藏图集';
                    btn.title = '取消收藏图集';
                } else {
                    btn.classList.remove('favorited');
                    btn.innerHTML = '❤️ 收藏图集';
                    btn.title = '收藏图集';
                }
            }
        }

        // 切换图集收藏状态
        async function toggleAlbumFavorite() {
            const btn = document.getElementById('albumFavoriteBtn');
            if (btn.disabled) return;

            btn.disabled = true;
            const originalText = btn.innerHTML;
            btn.innerHTML = '处理中...';

            try {
                const response = await fetch('/api/album-favorite', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        category: '{{ category }}',
                        album: '{{ album }}'
                    })
                });

                const data = await response.json();

                if (data.success) {
                    updateAlbumFavoriteButton(data.is_favorited);
                    showMessage(data.message, 'success');
                } else {
                    btn.innerHTML = originalText;
                    showMessage(data.message || '操作失败', 'error');
                }
            } catch (error) {
                console.error('切换图集收藏状态失败:', error);
                btn.innerHTML = originalText;
                showMessage('网络错误，请重试', 'error');
            } finally {
                btn.disabled = false;
            }
        }

        // 加载用户收藏状态
        async function loadUserFavorites() {
            try {
                console.log('🔄 Loading user favorites...');
                const response = await fetch('/api/user-favorites');
                const favorites = await response.json();
                userFavorites = new Set(favorites.map(f => f.image_id.toString()));
                console.log(`✅ Loaded ${userFavorites.size} user favorites`);
                return userFavorites;
            } catch (error) {
                console.error('❌ 加载收藏状态失败:', error);
                return new Set();
            }
        }

        // 加载图片数据 - 支持收藏模式
        async function loadImages(append = false) {
            if (loading) return;
            loading = true;

            try {
                // 根据是否为收藏模式选择不同的API
                const favoriteOnly = {{ 'true' if favorite_only else 'false' }};
                let apiUrl;

                if (favoriteOnly) {
                    // 收藏模式：只加载收藏的图片
                    apiUrl = `/api/album-favorites-only/{{ category }}/{{ album|urlencode }}?page=${currentPage}&limit=${itemsPerPage}`;
                } else {
                    // 普通模式：加载所有图片
                    apiUrl = `/api/album-images?category={{ category }}&album={{ album|urlencode }}&page=${currentPage}&limit=${itemsPerPage}`;
                }

                const response = await fetch(apiUrl);
                const data = await response.json();
                
                if (append) {
                    currentImages = currentImages.concat(data.images);
                    // 同步更新allImages数组
                    syncCurrentImagesToAllImages(data.images);
                } else {
                    currentImages = data.images;
                    document.getElementById('imageCount').textContent = data.total;
                    if (data.images.length > 0) {
                        document.getElementById('createDate').textContent = new Date(data.images[0].created_at).toLocaleDateString();
                    }
                    // 初始化时同步allImages
                    syncCurrentImagesToAllImages(data.images, false);
                }

                displayImages(append);

                hasMore = data.images.length === itemsPerPage;
                console.log(`=== loadImages Debug ===`);
                console.log(`Loaded ${data.images.length} images`);
                console.log(`itemsPerPage: ${itemsPerPage}`);
                console.log(`hasMore set to: ${hasMore}`);
                console.log(`currentImages.length after load: ${currentImages.length}`);
                console.log(`append mode: ${append}`);
                console.log(`currentPage: ${currentPage}`);
                console.log(`window.loadImagesCallback exists: ${!!window.loadImagesCallback}`);
                console.log(`=== End loadImages Debug ===`);

                updateLoadMoreButton();
                
            } catch (error) {
                console.error('加载图片失败:', error);
                document.getElementById('galleryGrid').innerHTML = '<div class="loading">加载失败，请刷新页面重试</div>';
            } finally {
                loading = false;
                // 触发加载完成回调
                if (window.loadImagesCallback) {
                    window.loadImagesCallback();
                    window.loadImagesCallback = null;
                }
            }
        }

        // 统一数据同步机制（增强版）
        function syncCurrentImagesToAllImages(newImages, append = true) {
            console.log(`=== syncCurrentImagesToAllImages Debug ===`);
            console.log(`allImages.length: ${allImages.length}`);
            console.log(`newImages.length: ${newImages.length}`);
            console.log(`append: ${append}`);

            if (allImages.length === 0) {
                console.log(`Skipping sync: allImages not initialized`);
                return; // 如果allImages未初始化，跳过同步
            }

            let syncedCount = 0;
            let newlyAddedCount = 0;

            newImages.forEach(newImage => {
                // 在allImages中找到对应的图片并更新
                const allImageIndex = allImages.findIndex(img => img.id === newImage.id);
                if (allImageIndex !== -1) {
                    // 更新allImages中的图片数据
                    allImages[allImageIndex] = {
                        ...allImages[allImageIndex], // 保留原有元数据
                        ...newImage // 添加完整数据
                    };
                    // 标记为已加载
                    modalLoadedImages.add(allImageIndex);
                    syncedCount++;
                    console.log(`✅ Synced image ${newImage.id} at allImages index ${allImageIndex}`);
                } else {
                    // 如果在allImages中找不到，可能是新增的图片，添加到末尾
                    if (append) {
                        allImages.push(newImage);
                        modalLoadedImages.add(allImages.length - 1);
                        totalImagesCount = allImages.length;
                        newlyAddedCount++;
                        console.log(`➕ Added new image ${newImage.id} to allImages at index ${allImages.length - 1}`);
                    } else {
                        console.log(`⚠️ Image ${newImage.id} not found in allImages and append=false`);
                    }
                }
            });

            console.log(`📊 Sync summary: ${syncedCount} updated, ${newlyAddedCount} added, total allImages: ${allImages.length}`);

            // 触发模态框数据更新事件
            if (syncedCount > 0 || newlyAddedCount > 0) {
                console.log(`🔄 Triggering modal data update event`);
                // 可以在这里添加其他需要在数据同步后执行的逻辑
            }

            console.log(`=== End syncCurrentImagesToAllImages Debug ===`);
        }

        // 显示图片
        function displayImages(append = false) {
            const grid = document.getElementById('galleryGrid');
            
            if (!append) {
                grid.innerHTML = '';
            }

            const startIndex = append ? currentImages.length - itemsPerPage : 0;
            const imagesToShow = append ? currentImages.slice(startIndex) : currentImages;

            imagesToShow.forEach((image, index) => {
                const actualIndex = startIndex + index;
                const isFavorited = userFavorites.has(image.id.toString());
                
                const imageCard = document.createElement('div');
                imageCard.className = 'image-card';
                // 修复图片路径：确保路径以 /images/ 开头
                const imagePath = image.path.startsWith('/images/') ? image.path : `/images/${image.path}`;

                imageCard.innerHTML = `
                    <img src="${imagePath}" alt="${image.filename}" loading="lazy"
                          onerror="this.src='${placeholderSvg}'">
                    <div class="image-overlay">
                        <div class="image-actions">
                            {% if session.user_id %}
                            <button class="action-btn ${isFavorited ? 'favorited' : ''}"
                                    onclick="event.stopPropagation(); toggleFavorite('${image.id}', this)"
                                    title="收藏"
                                    data-image-id="${image.id}">
                                ❤️
                            </button>
                            {% endif %}
                            <button class="action-btn" onclick="event.stopPropagation(); downloadImage('${imagePath}', '${image.filename}')" title="下载">
                                ⬇️
                            </button>
                            <button class="action-btn" onclick="event.stopPropagation(); openModal(${actualIndex})" title="查看">
                                👁️
                            </button>
                        </div>
                    </div>
                `;
                grid.appendChild(imageCard);
            });
        }

        // 加载更多图片（统一懒加载版本）
        function loadMoreImages() {
            console.log(`=== loadMoreImages Debug ===`);
            console.log(`hasMore: ${hasMore}`);
            console.log(`loading: ${loading}`);
            console.log(`currentPage: ${currentPage}`);
            console.log(`currentImages.length: ${currentImages.length}`);
            console.log(`LazyLoadManager.isGalleryLoading: ${LazyLoadManager.isGalleryLoading}`);
            console.log(`window.loadImagesCallback exists: ${!!window.loadImagesCallback}`);

            if (!hasMore) {
                console.log(`❌ Cannot load more: hasMore=${hasMore}`);
                if (window.loadImagesCallback) {
                    console.log(`🔄 Calling loadImagesCallback (hasMore=false)`);
                    const callback = window.loadImagesCallback;
                    window.loadImagesCallback = null;
                    setTimeout(callback, 0); // 异步调用
                }
                return;
            }

            if (loading) {
                console.log(`❌ Cannot load more: loading=${loading} - waiting for current load to complete`);
                if (window.loadImagesCallback) {
                    console.log(`🔄 Calling loadImagesCallback (loading=true)`);
                    const callback = window.loadImagesCallback;
                    window.loadImagesCallback = null;
                    setTimeout(callback, 0); // 异步调用
                }
                return;
            }

            console.log(`🚀 Loading more images, incrementing page from ${currentPage} to ${currentPage + 1}`);
            currentPage++;

            // 确保loadImages函数被正确调用
            try {
                loadImages(true);
                console.log(`📞 loadImages(true) called successfully`);
            } catch (error) {
                console.error(`❌ Error calling loadImages:`, error);
                if (window.loadImagesCallback) {
                    const callback = window.loadImagesCallback;
                    window.loadImagesCallback = null;
                    setTimeout(callback, 0);
                }
            }
        }

        // 更新加载更多按钮
        function updateLoadMoreButton() {
            const container = document.getElementById('loadMoreContainer');
            const btn = document.getElementById('loadMoreBtn');
            
            if (hasMore && currentImages.length > 0) {
                container.style.display = 'block';
                btn.disabled = loading;
                btn.textContent = loading ? '加载中...' : '加载更多';
            } else {
                container.style.display = 'none';
            }
        }

        // 设置视图大小
        function setViewSize(size) {
            const grid = document.getElementById('galleryGrid');
            const buttons = document.querySelectorAll('.view-btn');
            
            buttons.forEach(btn => btn.classList.remove('active'));
            document.querySelector(`[data-size="${size}"]`).classList.add('active');
            
            grid.className = `gallery-grid ${size}`;
        }



        // 打开模态框 - 增强版支持独立懒加载
        function openModal(index) {
            let globalIndex = index;

            // 如果有allImages数组，需要将currentImages的索引转换为allImages的全局索引
            if (allImages.length > 0 && currentImages.length > 0) {
                const currentImage = currentImages[index];
                if (currentImage && currentImage.id) {
                    // 在allImages中找到对应的图片
                    globalIndex = allImages.findIndex(img => img.id === currentImage.id);
                    if (globalIndex === -1) {
                        console.error('Image not found in allImages array');
                        globalIndex = index; // 回退到原始索引
                    }
                }
            }

            // 检查索引范围
            const maxIndex = allImages.length > 0 ? allImages.length : currentImages.length;
            if (globalIndex < 0 || globalIndex >= maxIndex) {
                console.error('Invalid image index:', globalIndex);
                return;
            }

            currentModalIndex = globalIndex;

            // 检查是否需要预加载更多图片
            if (allImages.length > 0) {
                checkModalPreload(globalIndex);
            }

            // 检查是否需要触发统一懒加载
            checkUnifiedLazyLoad();

            updateModal();
            const modal = document.getElementById('imageModal');
            if (modal) {
                modal.style.display = 'block';
                document.body.style.overflow = 'hidden';
            }
        }

        // 关闭模态框
        function closeModal() {
            const modal = document.getElementById('imageModal');
            if (modal) {
                modal.style.display = 'none';
                document.body.style.overflow = 'auto';
            }
        }

        // 更新模态框内容 - 增强版支持独立懒加载和收藏模式
        function updateModal() {
            console.log(`🔄 updateModal called, currentModalIndex: ${currentModalIndex}`);

            // 在收藏模式下，优先使用currentImages，因为allImages和currentImages应该是一致的
            const favoriteOnly = {{ 'true' if favorite_only else 'false' }};
            const imageArray = favoriteOnly ? currentImages : (allImages.length > 0 ? allImages : currentImages);
            const totalCount = favoriteOnly ? currentImages.length : (allImages.length > 0 ? totalImagesCount : currentImages.length);

            console.log(`📊 Modal data: favoriteOnly=${favoriteOnly}, imageArray.length=${imageArray.length}, totalCount=${totalCount}`);

            if (imageArray.length === 0) {
                console.error('❌ No images available for modal');
                return;
            }

            if (currentModalIndex < 0 || currentModalIndex >= imageArray.length) {
                console.error(`❌ Invalid modal index: ${currentModalIndex}, array length: ${imageArray.length}`);
                return;
            }

            const image = imageArray[currentModalIndex];
            if (!image) {
                console.error(`❌ Image not found at index: ${currentModalIndex}`);
                return;
            }

            console.log(`🖼️ Updating modal with image:`, image);

            // 修复图片路径：确保路径以 /images/ 开头
            const imagePath = image.path.startsWith('/images/') ? image.path : `/images/${image.path}`;
            document.getElementById('modalImage').src = imagePath;
            document.getElementById('modalInfo').textContent = `${currentModalIndex + 1} / ${totalCount}`;

            // 更新模态框收藏按钮状态
            updateModalFavoriteButton();

            // 检查是否需要预加载更多图片（收藏模式下跳过）
            if (!favoriteOnly && allImages.length > 0) {
                checkModalPreload(currentModalIndex);
            }
        }

        // 更新模态框收藏按钮状态 - 增强边界检查
        function updateModalFavoriteButton() {
            const favoriteBtn = document.getElementById('modalFavorite');
            if (!favoriteBtn) return;

            // 严格的边界检查
            if (!currentImages || currentImages.length === 0) {
                console.warn('⚠️ updateModalFavoriteButton: currentImages is empty');
                return;
            }

            if (currentModalIndex < 0 || currentModalIndex >= currentImages.length) {
                console.error(`❌ updateModalFavoriteButton: Invalid index ${currentModalIndex}, array length: ${currentImages.length}`);
                return;
            }

            const image = currentImages[currentModalIndex];
            if (!image || !image.id) {
                console.error(`❌ updateModalFavoriteButton: Invalid image data at index ${currentModalIndex}`, image);
                return;
            }

            const isFavorited = userFavorites.has(image.id.toString());

            // 更新按钮样式和文本
            if (isFavorited) {
                favoriteBtn.classList.add('favorited');
                favoriteBtn.textContent = '💖';
            } else {
                favoriteBtn.classList.remove('favorited');
                favoriteBtn.textContent = '❤️';
            }

            // 确保基础class正确
            favoriteBtn.className = favoriteBtn.className.replace(/modal-favorite/g, '').trim();
            favoriteBtn.classList.add('modal-favorite');
        }



        // 切换模态框收藏状态
        function toggleModalFavorite() {
            if (currentImages.length === 0) return;

            const image = currentImages[currentModalIndex];
            toggleFavorite(image.id, document.getElementById('modalFavorite'));
        }

        // 上一张图片 - 增强版支持独立懒加载
        function prevImage() {
            const maxIndex = allImages.length > 0 ? allImages.length : currentImages.length;
            if (currentModalIndex > 0) {
                currentModalIndex--;
                updateModal();

                // 检查是否需要触发统一懒加载
                checkUnifiedLazyLoad();
            }
        }

        // 下一张图片 - 增强版支持独立懒加载
        function nextImage() {
            const maxIndex = allImages.length > 0 ? allImages.length : currentImages.length;
            if (currentModalIndex < maxIndex - 1) {
                currentModalIndex++;
                updateModal();

                // 检查是否需要触发统一懒加载
                checkUnifiedLazyLoad();
            }
        }

        // 统一懒加载触发条件检查
        function checkUnifiedLazyLoad() {
            console.log(`=== checkUnifiedLazyLoad Debug ===`);
            console.log(`allImages.length: ${allImages.length}`);
            console.log(`currentImages.length: ${currentImages.length}`);
            console.log(`currentModalIndex: ${currentModalIndex}`);
            console.log(`hasMore: ${hasMore}`);
            console.log(`loading: ${loading}`);
            console.log(`modalLoadingBatch: ${modalLoadingBatch}`);
            console.log(`LazyLoadManager.isGalleryLoading: ${LazyLoadManager.isGalleryLoading}`);
            console.log(`LazyLoadManager.isModalLoading: ${LazyLoadManager.isModalLoading}`);

            // 如果没有图片数据，直接返回
            if (currentImages.length === 0) {
                console.log(`Early return: currentImages is empty`);
                return;
            }

            // 收集所有触发条件
            const triggers = [];
            let currentImageInGallery = -1;

            // === 图集页面触发条件检查 ===
            // 如果有allImages，使用allImages逻辑
            if (allImages.length > 0) {
                const currentImage = allImages[currentModalIndex];
                if (currentImage && currentImage.id) {
                    currentImageInGallery = currentImages.findIndex(img =>
                        img.id === currentImage.id
                    );
                }
            } else {
                // 如果没有allImages，直接使用currentModalIndex
                if (currentModalIndex < currentImages.length) {
                    currentImageInGallery = currentModalIndex;
                }
            }

            if (currentImageInGallery !== -1) {
                const galleryProgress = (currentImageInGallery + 1) / currentImages.length;
                const remainingInGallery = currentImages.length - currentImageInGallery - 1;

                console.log(`Gallery: ${currentImageInGallery + 1}/${currentImages.length} (${(galleryProgress * 100).toFixed(1)}%)`);

                // 图集页面触发条件
                if (galleryProgress >= MODAL_SMART_PRELOAD_RATIO) {
                    triggers.push({
                        type: 'gallery_progress',
                        reason: `Gallery progress ${(galleryProgress * 100).toFixed(1)}% >= 75%`,
                        priority: 1
                    });
                }

                if (remainingInGallery <= 5) {
                    triggers.push({
                        type: 'gallery_distance',
                        reason: `Near gallery end (${remainingInGallery} remaining)`,
                        priority: 2
                    });
                }
            }

            // === 模态框触发条件检查 ===
            if (allImages.length > 0) {
                // 检查模态框预加载条件
                const modalProgress = (currentModalIndex + 1) / totalImagesCount;

                // 找到当前已加载的最大连续索引
                let maxLoadedIndex = -1;
                for (let i = 0; i < totalImagesCount; i++) {
                    if (modalLoadedImages.has(i)) {
                        maxLoadedIndex = i;
                    } else {
                        break;
                    }
                }

                const preloadedAhead = maxLoadedIndex >= currentModalIndex ? maxLoadedIndex - currentModalIndex + 1 : 0;
                const loadedContentProgress = preloadedAhead > 0 ?
                    Math.min(1, (currentModalIndex - (maxLoadedIndex - preloadedAhead + 1) + 1) / preloadedAhead) : 1;

                console.log(`Modal: ${currentModalIndex + 1}/${totalImagesCount}, preloaded ahead: ${preloadedAhead}`);

                // 模态框触发条件
                if (loadedContentProgress >= MODAL_SMART_PRELOAD_RATIO && maxLoadedIndex < totalImagesCount - 1) {
                    triggers.push({
                        type: 'modal_progress',
                        reason: `Modal loaded content progress ${(loadedContentProgress * 100).toFixed(1)}% >= 75%`,
                        priority: 1
                    });
                }

                if (preloadedAhead < MODAL_BATCH_SIZE && maxLoadedIndex < totalImagesCount - 1) {
                    triggers.push({
                        type: 'modal_insufficient',
                        reason: `Insufficient modal preload (${preloadedAhead} < ${MODAL_BATCH_SIZE})`,
                        priority: 3
                    });
                }
            }

            // === 统一触发决策 ===
            if (triggers.length > 0) {
                // 按优先级排序，选择最高优先级的触发器
                triggers.sort((a, b) => a.priority - b.priority);
                const primaryTrigger = triggers[0];

                console.log(`Found ${triggers.length} triggers:`, triggers.map(t => t.reason));
                console.log(`Primary trigger: ${primaryTrigger.reason}`);

                // 使用统一懒加载管理器触发加载
                LazyLoadManager.triggerUnifiedLazyLoad(primaryTrigger.reason, {
                    currentIndex: currentModalIndex,
                    galleryIndex: currentImageInGallery,
                    triggers: triggers
                });
            } else {
                console.log(`❌ No triggers activated`);
            }

            console.log(`=== End checkUnifiedLazyLoad Debug ===`);
        }

        // 切换收藏 - 增强版支持实时更新所有相关按钮
        async function toggleFavorite(imageId, buttonElement) {
            try {
                const response = await fetch('/api/favorite', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        image_id: imageId
                    })
                });

                const result = await response.json();
                if (result.success) {
                    // 更新全局收藏状态
                    if (result.favorited) {
                        userFavorites.add(imageId.toString());
                    } else {
                        userFavorites.delete(imageId.toString());
                    }

                    // 更新所有相关的收藏按钮
                    updateAllFavoriteButtons(imageId, result.favorited);

                    // 显示提示
                    const message = result.favorited ? '已收藏' : '已取消收藏';
                    showToast(message);
                }
            } catch (error) {
                console.error('收藏操作失败:', error);
                showToast('操作失败，请重试');
            }
        }

        // 更新所有相关的收藏按钮状态
        function updateAllFavoriteButtons(imageId, isFavorited) {
            const imageIdStr = imageId.toString();
            console.log(`🔄 Updating all favorite buttons for image ${imageIdStr}, favorited: ${isFavorited}`);

            // 1. 更新所有图片卡片上的收藏按钮
            const cardButtons = document.querySelectorAll(`[data-image-id="${imageIdStr}"]`);
            console.log(`📋 Found ${cardButtons.length} card buttons to update`);

            cardButtons.forEach(btn => {
                if (isFavorited) {
                    btn.classList.add('favorited');
                } else {
                    btn.classList.remove('favorited');
                }
            });

            // 2. 更新模态框中的收藏按钮（如果当前显示的是这张图片）
            const modal = document.getElementById('imageModal');
            const modalFavorite = document.getElementById('modalFavorite');

            if (modal && modal.style.display === 'block' && modalFavorite) {
                // 检查当前模态框显示的是否是这张图片
                const currentImage = currentImages[currentModalIndex];
                if (currentImage && currentImage.id.toString() === imageIdStr) {
                    console.log(`💖 Updating modal favorite button: ${isFavorited ? 'favorited' : 'not favorited'}`);
                    if (isFavorited) {
                        modalFavorite.classList.add('favorited');
                        modalFavorite.textContent = '💖';
                    } else {
                        modalFavorite.classList.remove('favorited');
                        modalFavorite.textContent = '❤️';
                    }
                } else {
                    console.log(`ℹ️ Modal is showing different image, not updating modal button`);
                }
            } else {
                console.log(`ℹ️ Modal not open or modal favorite button not found`);
            }
        }

        // 显示提示消息 - 增强版支持不同类型
        function showToast(message, type = 'success') {
            // 创建提示元素
            const toast = document.createElement('div');

            let backgroundColor;
            switch(type) {
                case 'error':
                    backgroundColor = 'rgba(220, 53, 69, 0.9)';
                    break;
                case 'info':
                    backgroundColor = 'rgba(13, 202, 240, 0.9)';
                    break;
                case 'warning':
                    backgroundColor = 'rgba(255, 193, 7, 0.9)';
                    break;
                default: // success
                    backgroundColor = 'rgba(25, 135, 84, 0.9)';
            }

            toast.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: ${backgroundColor};
                color: white;
                padding: 1rem 2rem;
                border-radius: 25px;
                z-index: 3000;
                font-size: 1rem;
                pointer-events: none;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
                backdrop-filter: blur(10px);
            `;
            toast.textContent = message;
            document.body.appendChild(toast);

            // 3秒后移除
            setTimeout(() => {
                if (document.body.contains(toast)) {
                    document.body.removeChild(toast);
                }
            }, 3000);
        }

        // 下载图片
        function downloadImage(imagePath, filename) {
            const link = document.createElement('a');
            link.href = imagePath;
            link.download = filename;
            link.click();
        }

        // 收藏全部 - 修复懒加载限制问题
        async function favoriteAll() {
            try {
                // 首先获取图集的总图片数量
                const albumName = '{{ album }}';
                const category = '{{ category }}';

                showToast('正在获取图集信息...', 'info');

                // 使用元数据API获取完整的图片列表
                const metadataResponse = await fetch(`/api/album/${category}/${encodeURIComponent(albumName)}/images?metadata_only=true`);
                const metadataData = await metadataResponse.json();

                const totalImages = metadataData.images ? metadataData.images.length : currentImages.length;

                if (!confirm(`确定要收藏这个图集的所有 ${totalImages} 张图片吗？`)) {
                    return;
                }

                showToast('正在批量收藏图片...', 'info');

                // 使用后端批量收藏API
                const response = await fetch('/api/favorite-album-all', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        category: category,
                        album: albumName
                    })
                });

                const result = await response.json();
                if (result.success) {
                    // 更新本地收藏状态
                    if (result.favorited_ids) {
                        result.favorited_ids.forEach(id => {
                            userFavorites.add(id.toString());
                        });
                    }

                    // 更新所有收藏按钮状态
                    document.querySelectorAll('.action-btn[data-image-id]').forEach(btn => {
                        const imageId = btn.getAttribute('data-image-id');
                        if (userFavorites.has(imageId)) {
                            btn.classList.add('favorited');
                        }
                    });

                    showToast(`成功收藏了 ${result.count} 张图片`);
                } else {
                    showToast(result.message || '批量收藏失败', 'error');
                }
            } catch (error) {
                console.error('批量收藏失败:', error);
                showToast('批量收藏失败，请重试', 'error');
            }
        }

        // 查看原图集 - 跳转到完整图集页面
        function viewOriginalAlbum() {
            const category = '{{ category }}';
            const album = '{{ album }}';
            const url = `/album/${encodeURIComponent(category)}/${encodeURIComponent(album)}`;
            window.location.href = url;
        }

        // 取消收藏全部 - 收藏模式专用
        async function unfavoriteAll() {
            const favoriteCount = currentImages.length;
            if (!confirm(`确定要取消收藏这个图集的所有 ${favoriteCount} 张图片吗？`)) {
                return;
            }

            try {
                showToast('正在取消收藏...', 'info');

                // 批量取消收藏
                const promises = currentImages.map(image =>
                    fetch('/api/favorite', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ image_id: image.id })
                    })
                );

                await Promise.all(promises);

                showToast(`已取消收藏 ${favoriteCount} 张图片`, 'success');

                // 刷新页面或返回收藏页面
                setTimeout(() => {
                    window.location.href = '/favorites';
                }, 1500);

            } catch (error) {
                console.error('批量取消收藏失败:', error);
                showToast('批量取消收藏失败，请重试', 'error');
            }
        }

        // 下载全部
        function downloadAll() {
            if (!confirm(`确定要下载这个图集的所有 ${currentImages.length} 张图片吗？`)) {
                return;
            }
            
            currentImages.forEach((image, index) => {
                setTimeout(() => {
                    downloadImage(image.path, image.filename);
                }, index * 500); // 每500ms下载一张，避免浏览器限制
            });
        }

        // 键盘事件
        document.addEventListener('keydown', function(e) {
            const modal = document.getElementById('imageModal');
            if (modal.style.display === 'block') {
                if (e.key === 'Escape') {
                    closeModal();
                } else if (e.key === 'ArrowLeft') {
                    prevImage();
                } else if (e.key === 'ArrowRight') {
                    nextImage();
                }
            }
        });

        // 点击模态框外部关闭
        document.getElementById('imageModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        // 无限滚动
        window.addEventListener('scroll', function() {
            if ((window.innerHeight + window.scrollY) >= document.body.offsetHeight - 1000) {
                if (hasMore && !loading) {
                    loadMoreImages();
                }
            }
        });

        // 触摸滑动支持（手机端）- 改进版
        let touchStartX = 0;
        let touchEndX = 0;
        let touchStartY = 0;
        let touchEndY = 0;

        document.getElementById('imageModal').addEventListener('touchstart', function(e) {
            touchStartX = e.changedTouches[0].screenX;
            touchStartY = e.changedTouches[0].screenY;
        }, { passive: true });

        document.getElementById('imageModal').addEventListener('touchend', function(e) {
            touchEndX = e.changedTouches[0].screenX;
            touchEndY = e.changedTouches[0].screenY;
            handleSwipe();
        }, { passive: true });

        function handleSwipe() {
            const swipeThreshold = 50;
            const diffX = touchStartX - touchEndX;
            const diffY = Math.abs(touchStartY - touchEndY);

            // 只有水平滑动距离大于垂直滑动距离时才触发切换
            if (Math.abs(diffX) > swipeThreshold && Math.abs(diffX) > diffY) {
                if (diffX > 0) {
                    // 向左滑动，显示下一张
                    nextImage();
                } else {
                    // 向右滑动，显示上一张
                    prevImage();
                }
            }
        }

        // User dropdown functionality - 重新设计
        function toggleUserDropdown() {
            if (window.innerWidth <= 768) {
                // 移动端使用模态框
                const mobileModal = document.getElementById('mobileDropdownModal');
                if (mobileModal) {
                    mobileModal.classList.toggle('show');
                }
            } else {
                // 桌面端使用下拉菜单
                const dropdown = document.getElementById('userDropdown');
                if (dropdown) {
                    dropdown.classList.toggle('show');
                }
            }
        }

        // 关闭下拉菜单
        function closeDropdowns() {
            const dropdown = document.getElementById('userDropdown');
            const mobileModal = document.getElementById('mobileDropdownModal');

            if (dropdown) dropdown.classList.remove('show');
            if (mobileModal) mobileModal.classList.remove('show');
        }

        // 点击外部关闭
        document.addEventListener('click', function(event) {
            const userInfo = document.querySelector('.user-dropdown');
            const mobileModal = document.getElementById('mobileDropdownModal');

            // 如果点击的不是用户信息区域
            if (userInfo && !userInfo.contains(event.target)) {
                closeDropdowns();
            }

            // 如果点击的是移动端模态框背景
            if (event.target === mobileModal) {
                closeDropdowns();
            }
        });

        // 触摸事件支持
        document.addEventListener('touchstart', function(event) {
            const userInfo = document.querySelector('.user-dropdown');
            const mobileModal = document.getElementById('mobileDropdownModal');

            if (userInfo && !userInfo.contains(event.target) && event.target !== mobileModal) {
                closeDropdowns();
            }
        });
    </script>
</body>
</html>'''

# 主页面HTML模板
MAIN_TEMPLATE = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>春色写真馆 - Spring Gallery</title>
    <link rel="icon" type="image/svg+xml" href="/favicon.ico">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'PingFang SC', 'Hiragino Sans GB', 'Noto Sans CJK SC', 'Source Han Sans SC', 'Microsoft YaHei', '微软雅黑', sans-serif;
            background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 25%, #f0fff4 50%, #fff8dc 75%, #fdf5e6 100%);
            min-height: 100vh;
            color: #5d4e37;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            padding: 1rem 0;
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
            box-shadow: 0 2px 20px rgba(255, 182, 193, 0.3);
        }
        
        .nav {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
        }
        
        .logo {
            font-size: 2rem;
            font-weight: bold;
            background: linear-gradient(45deg, #ff69b4, #ffa500, #98fb98);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        .nav-links {
            display: flex;
            gap: 2rem;
            align-items: center;
        }
        
        .nav-link {
            color: #8b4513;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .nav-link:hover {
            background: linear-gradient(45deg, rgba(255, 182, 193, 0.3), rgba(255, 218, 185, 0.3));
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 182, 193, 0.4);
        }
        
        .user-info {
            display: flex;
            align-items: center;
            justify-content: center; /* 水平居中 */
            gap: 0.3rem;
            max-width: 120px; /* 减小整体宽度 */
            flex-shrink: 0; /* 防止压缩 */
            position: relative;
        }

        .user-info .avatar {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            flex-shrink: 0; /* 头像不压缩 */
        }

        .user-info span {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 50px; /* 进一步减小昵称宽度 */
            font-size: 0.8rem;
            cursor: pointer;
            position: relative;
            display: inline-block;
            vertical-align: middle;
            flex-shrink: 1; /* 允许文字压缩 */
            min-width: 0; /* 允许收缩到0 */
        }

        /* User dropdown menu - 重新设计 */
        .user-dropdown {
            position: relative;
            display: inline-block;
        }

        .user-dropdown-content {
            display: none;
            position: absolute;
            right: 0;
            top: 100%;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            min-width: 150px;
            box-shadow: 0 8px 25px rgba(255, 182, 193, 0.3);
            border-radius: 10px;
            z-index: 1001;
            border: 1px solid rgba(255, 182, 193, 0.2);
            overflow: hidden;
            margin-top: 5px;
        }

        .user-dropdown-content.show {
            display: block;
        }

        .dropdown-item {
            display: block;
            padding: 0.8rem 1rem;
            color: #8b4513;
            text-decoration: none;
            transition: all 0.3s ease;
            border-bottom: 1px solid rgba(255, 182, 193, 0.1);
        }

        .dropdown-item:last-child {
            border-bottom: none;
        }

        .dropdown-item:hover {
            background: linear-gradient(45deg, #ff69b4, #ffa500);
            color: white;
            transform: translateX(5px);
        }

        /* 移动端模态框样式下拉菜单 */
        .mobile-dropdown-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 9999;
            justify-content: center;
            align-items: center;
        }

        .mobile-dropdown-modal.show {
            display: flex;
        }

        .mobile-dropdown-content {
            background: white;
            border-radius: 15px;
            padding: 1rem;
            min-width: 200px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            animation: slideUp 0.3s ease;
        }

        @keyframes slideUp {
            from {
                transform: translateY(50px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .mobile-dropdown-item {
            display: block;
            padding: 1rem;
            color: #8b4513;
            text-decoration: none;
            text-align: center;
            border-bottom: 1px solid rgba(255, 182, 193, 0.2);
            transition: all 0.3s ease;
        }

        .mobile-dropdown-item:last-child {
            border-bottom: none;
        }

        .mobile-dropdown-item:hover {
            background: linear-gradient(45deg, #ff69b4, #ffa500);
            color: white;
        }
        
        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 2px solid #ffb6c1;
            object-fit: cover;
        }
        
        .main-content {
            margin-top: 100px;
            padding: 2rem;
            max-width: 1400px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .hero-section {
            text-align: center;
            padding: 4rem 0;
            background: linear-gradient(135deg, rgba(255, 182, 193, 0.1), rgba(255, 218, 185, 0.1));
            border-radius: 30px;
            margin-bottom: 3rem;
            position: relative;
            overflow: hidden;
        }
        
        .hero-section::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 182, 193, 0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        
        .hero-title {
            font-size: 3.5rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #ff69b4, #ffa500, #98fb98, #dda0dd);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            position: relative;
            z-index: 1;
        }
        
        .hero-subtitle {
            font-size: 1.2rem;
            color: #8b4513;
            margin-bottom: 2rem;
            position: relative;
            z-index: 1;
        }
        
        .search-container {
            position: relative;
            max-width: 700px;
            margin: 0 auto 2rem;
            z-index: 1;
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }

        .search-input {
            flex: 1;
            padding: 1rem 1.5rem;
            border: 2px solid #ffb6c1;
            border-radius: 50px;
            font-size: 1rem;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .search-category-select {
            padding: 1rem 1.5rem;
            border: 2px solid #ffb6c1;
            border-radius: 50px;
            font-size: 1rem;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            min-width: 120px;
            cursor: pointer;
        }

        .search-category-select:focus {
            outline: none;
            border-color: #ff69b4;
            box-shadow: 0 0 20px rgba(255, 105, 180, 0.3);
        }
        
        .search-input:focus {
            outline: none;
            border-color: #ff69b4;
            box-shadow: 0 0 20px rgba(255, 105, 180, 0.3);
        }
        
        .search-btn {
            background: linear-gradient(45deg, #ff69b4, #ffa500);
            border: none;
            padding: 1rem 1.5rem;
            border-radius: 50px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
            min-width: 60px;
        }
        
        .search-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 15px rgba(255, 105, 180, 0.4);
        }
        
        .view-toggle {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
            position: relative;
            z-index: 1;
        }

        .view-toggle-container {
            display: flex;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 25px;
            padding: 0.3rem;
            box-shadow: 0 10px 30px rgba(255, 182, 193, 0.2);
            border: 2px solid rgba(255, 182, 193, 0.3);
        }

        .toggle-btn {
            padding: 0.8rem 1.5rem;
            border: none;
            background: transparent;
            color: #8b4513;
            border-radius: 20px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
            white-space: nowrap;
        }

        .toggle-btn:hover {
            background: rgba(255, 182, 193, 0.1);
        }

        .toggle-btn.active {
            background: linear-gradient(45deg, #ffb6c1, #ffc0cb);
            color: white;
            box-shadow: 0 3px 10px rgba(255, 105, 180, 0.3);
        }
        
        .categories {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .albums-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .category-card, .album-card {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            border: 1px solid rgba(255, 182, 193, 0.3);
        }
        
        .category-card::before, .album-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 182, 193, 0.2), transparent);
            transition: left 0.5s;
        }
        
        .category-card:hover::before, .album-card:hover::before {
            left: 100%;
        }
        
        .category-card:hover, .album-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(255, 182, 193, 0.3);
        }
        
        .category-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            display: block;
        }
        
        .album-cover {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 15px;
            margin-bottom: 1rem;
        }
        
        .category-title, .album-title {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
            color: #8b4513;
        }
        
        .category-count, .album-count {
            color: #cd853f;
            font-size: 0.9rem;
        }
        
        .filter-bar {
            background: rgba(255, 255, 255, 0.9);
            padding: 1rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            align-items: center;
        }
        
        .filter-select {
            padding: 0.5rem 1rem;
            border: 1px solid #ffb6c1;
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.9);
            color: #8b4513;
        }
        
        .loading {
            text-align: center;
            padding: 2rem;
            font-size: 1.2rem;
            color: #8b4513;
        }
        
        /* 优化的分页样式 - 遵循设计模式 */
        .pagination-wrapper {
            width: 100%;
            display: flex;
            justify-content: center;
            margin: 2rem 0;
        }

        .pagination-container {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem 1.5rem;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 25px;
            box-shadow: 0 4px 15px rgba(255, 182, 193, 0.2);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 182, 193, 0.3);
            width: fit-content;
            max-width: 90%;
            flex-wrap: wrap;
        }

        .page-btn {
            padding: 0.5rem 0.8rem;
            border: 1px solid #ffb6c1;
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.9);
            color: #8b4513;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            min-width: 40px;
            text-align: center;
            white-space: nowrap;
        }

        .page-btn:hover:not(:disabled):not(.active) {
            background: linear-gradient(45deg, #ff69b4, #ffa500);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 105, 180, 0.4);
        }

        .page-btn.active {
            background: linear-gradient(45deg, #ff69b4, #ffa500);
            color: white;
            border-color: #ff69b4;
            font-weight: bold;
        }

        .page-btn:disabled {
            opacity: 0.4;
            cursor: not-allowed;
            background: rgba(200, 200, 200, 0.3);
            color: #999;
            transform: none;
            box-shadow: none;
        }

        .page-ellipsis {
            padding: 0.6rem 0.4rem;
            color: #8b4513;
            font-weight: 600;
            font-size: 0.9rem;
            opacity: 0.7;
        }

        .page-nav-btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 20px;
            background: linear-gradient(45deg, #ff69b4, #ffa500);
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            white-space: nowrap;
        }

        .page-nav-btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 105, 180, 0.4);
        }

        .page-nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            background: rgba(200, 200, 200, 0.5);
        }

        /* 页面跳转功能样式 */
        .page-jump-container {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-left: 1rem;
            padding-left: 1rem;
            border-left: 1px solid rgba(255, 182, 193, 0.3);
        }

        .page-jump-label {
            font-size: 0.85rem;
            color: #8b4513;
            white-space: nowrap;
        }

        .page-jump-input {
            width: 60px;
            padding: 0.4rem 0.6rem;
            border: 1px solid #ffb6c1;
            border-radius: 15px;
            background: rgba(255, 255, 255, 0.9);
            color: #8b4513;
            font-size: 0.85rem;
            text-align: center;
            transition: all 0.3s ease;
        }

        .page-jump-input:focus {
            outline: none;
            border-color: #ff69b4;
            box-shadow: 0 0 0 2px rgba(255, 105, 180, 0.2);
        }

        .page-jump-select {
            padding: 0.4rem 0.6rem;
            border: 1px solid #ffb6c1;
            border-radius: 15px;
            background: rgba(255, 255, 255, 0.9);
            color: #8b4513;
            font-size: 0.85rem;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 80px;
        }

        .page-jump-select:focus {
            outline: none;
            border-color: #ff69b4;
            box-shadow: 0 0 0 2px rgba(255, 105, 180, 0.2);
        }

        .page-jump-btn {
            padding: 0.4rem 0.8rem;
            border: none;
            border-radius: 15px;
            background: linear-gradient(45deg, #98fb98, #87ceeb);
            color: #2f4f4f;
            cursor: pointer;
            font-size: 0.85rem;
            transition: all 0.3s ease;
            white-space: nowrap;
        }

        .page-jump-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(152, 251, 152, 0.4);
        }

        .page-jump-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        /* 收藏标签样式 - 完全复制view-toggle的样式 */
        .favorites-tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }



        .favorites-tabs .toggle-btn {
            padding: 0.8rem 1.5rem;
            border: none;
            background: transparent;
            color: #8b4513;
            border-radius: 20px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
            white-space: nowrap;
        }

        .favorites-tabs .toggle-btn:hover {
            background: rgba(255, 182, 193, 0.1);
        }

        .favorites-tabs .toggle-btn.active {
            background: linear-gradient(45deg, #ffb6c1, #ffc0cb);
            color: white;
            box-shadow: 0 3px 10px rgba(255, 105, 180, 0.3);
        }


        /* 视图模式切换样式 - 直接复制clear-all-btn的成功样式 */
        .view-mode-toggle {
            display: flex;
            gap: 0.5rem;
        }

        .view-mode-btn {
            padding: 0.5rem 1rem;
            background: rgba(255, 255, 255, 0.9);
            color: #8b4513;
            border: 1px solid #ffb6c1;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            white-space: nowrap;
        }

        .view-mode-btn:hover,
        .view-mode-btn.active {
            background: linear-gradient(45deg, #ff69b4, #ffa500);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 105, 180, 0.4);
        }

        /* 备用样式 - 如果上面不工作，使用这个 */
        .view-mode-btn-alt {
            padding: 0.5rem 1rem;
            background: linear-gradient(45deg, #98fb98, #87ceeb);
            color: #2f4f4f;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            white-space: nowrap;
        }

        .view-mode-btn-alt:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(152, 251, 152, 0.4);
        }

        .view-mode-btn-alt.active {
            background: linear-gradient(45deg, #ff69b4, #ffa500);
            color: white;
        }

        @media (max-width: 768px) {
            .nav {
                flex-direction: row;
                gap: 0.5rem;
                padding: 0 0.5rem;
                align-items: center;
                justify-content: space-between;
            }

            .logo {
                font-size: 1.2rem;
                flex-shrink: 0;
            }

            .nav-links {
                gap: 0.3rem;
                flex-wrap: nowrap;
                justify-content: flex-end;
                align-items: center;
                overflow-x: auto;
                flex: 1;
                min-width: 0;
            }

            .nav-link {
                font-size: 0.8rem;
                padding: 0.3rem 0.5rem;
                white-space: nowrap;
                flex-shrink: 0;
            }

            .user-info {
                flex-direction: row;
                gap: 0.2rem;
                align-items: center;
                justify-content: center; /* 移动端也居中 */
                max-width: 80px; /* 进一步减小移动端宽度 */
                flex-shrink: 0;
            }

            .user-info .avatar {
                width: 20px;
                height: 20px;
                flex-shrink: 0; /* 头像不压缩 */
            }

            .user-info span {
                max-width: 35px; /* 大幅减小移动端文字宽度 */
                font-size: 0.65rem;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                display: inline-block;
                vertical-align: middle;
                flex-shrink: 1; /* 允许文字压缩 */
                min-width: 0; /* 允许收缩到0 */
            }

            /* 移动端隐藏桌面版下拉菜单，使用模态框版本 */
            .user-dropdown-content {
                display: none !important;
            }

            /* 移动端搜索容器调整 */
            .search-container {
                flex-direction: column;
                gap: 0.5rem;
                max-width: 90%;
            }

            .search-input, .search-category-select, .search-btn {
                width: 100%;
                margin: 0;
            }

            .hero-title {
                font-size: 2.5rem;
            }

            .categories, .albums-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 1rem;
            }

            .filter-bar {
                flex-direction: column;
                align-items: stretch;
            }

            .pagination-container {
                gap: 0.3rem;
                padding: 0.8rem 1rem;
                max-width: 95%;
                flex-wrap: wrap;
                flex-direction: column;
            }

            .pagination-wrapper {
                margin: 1.5rem 0;
            }

            .page-btn, .page-nav-btn {
                padding: 0.4rem 0.6rem;
                font-size: 0.8rem;
                min-width: 35px;
            }

            .page-nav-btn {
                padding: 0.4rem 0.8rem;
            }

            .page-jump-container {
                margin-left: 0;
                padding-left: 0;
                border-left: none;
                border-top: 1px solid rgba(255, 182, 193, 0.3);
                padding-top: 0.8rem;
                margin-top: 0.8rem;
                justify-content: center;
                flex-wrap: wrap;
            }

            .page-jump-input {
                width: 50px;
                font-size: 0.8rem;
            }

            .page-jump-select {
                font-size: 0.8rem;
                min-width: 70px;
            }

            .page-jump-btn {
                font-size: 0.8rem;
                padding: 0.3rem 0.6rem;
            }

            .view-toggle-container {
                flex-direction: column;
                gap: 0.3rem;
                padding: 0.5rem;
            }

            .toggle-btn {
                padding: 0.6rem 1rem;
                font-size: 0.9rem;
            }

            .favorites-tabs .toggle-btn {
                padding: 0.6rem 1rem;
                font-size: 0.9rem;
            }

            .view-mode-toggle {
                flex-direction: row;
                justify-content: center;
                gap: 0.3rem;
            }

            .view-mode-btn {
                padding: 0.4rem 0.8rem;
                font-size: 0.8rem;
            }
        }
        
        .footer {
            background: linear-gradient(135deg, rgba(255, 182, 193, 0.2), rgba(255, 218, 185, 0.2));
            padding: 3rem 0;
            margin-top: 4rem;
            text-align: center;
            color: #8b4513;
        }
        
        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }
        
        .footer-links {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }
        
        .footer-link {
            color: #8b4513;
            text-decoration: none;
            transition: color 0.3s ease;
        }
        
        .footer-link:hover {
            color: #ff69b4;
        }
    </style>
</head>
<body>
    <header class="header">
        <nav class="nav">
            <a href="/" class="logo" style="text-decoration: none; color: inherit;">🌸 春色写真馆</a>
            <div class="nav-links">
                <a href="/" class="nav-link">首页</a>
                <a href="/gallery" class="nav-link">图库</a>
                <a href="/favorites" class="nav-link">收藏</a>
                {% if session.user_id %}
                    <a href="/admin" class="nav-link">管理</a>
                    <div class="user-info user-dropdown">
                        <img src="{{ session.avatar or default_avatar }}" alt="头像" class="avatar">
                        <span onclick="toggleUserDropdown()" title="点击查看选项">{{ session.username }}</span>
                        <div class="user-dropdown-content" id="userDropdown">
                            <a href="/change-password" class="dropdown-item">修改密码</a>
                            <a href="/logout" class="dropdown-item">退出</a>
                        </div>
                    </div>
                {% else %}
                    <a href="/login" class="nav-link">登录</a>
                    <a href="/register" class="nav-link">注册</a>
                {% endif %}
            </div>
        </nav>
    </header>

    <main class="main-content">
        <section class="hero-section">
            <h1 class="hero-title">春色写真馆</h1>
            <p class="hero-subtitle">发现美的瞬间，感受春天的温暖</p>
            <div class="search-container">
                <input type="text" class="search-input" placeholder="搜索你喜欢的内容..." id="searchInput">
                <select class="search-category-select" id="searchCategoryFilter">
                    <option value="">所有分类</option>
                    <option value="korea">韩系</option>
                    <option value="cosplay">角色扮演</option>
                    <option value="japan">日系</option>
                    <option value="gravure">写真</option>
                    <option value="chinese">中式</option>
                    <option value="thailand">泰式</option>
                </select>
                <button class="search-btn" onclick="performSearch()">🔍</button>
            </div>
            <div class="view-toggle">
                <div class="view-toggle-container">
                    <button class="toggle-btn active" onclick="switchView('categories')" id="categoriesBtn">分类浏览</button>
                    <button class="toggle-btn" onclick="switchView('albums')" id="albumsBtn">图集浏览</button>
                </div>
            </div>
        </section>

        <div class="filter-bar">
            <select class="filter-select" id="categoryFilter" onchange="filterContent()">
                <option value="">所有分类</option>
                <option value="korea">韩系</option>
                <option value="cosplay">角色扮演</option>
                <option value="japan">日系</option>
                <option value="gravure">写真</option>
                <option value="chinese">中式</option>
                <option value="thailand">泰式</option>
            </select>
            <select class="filter-select" id="sortFilter" onchange="filterContent()">
                <option value="newest">最新</option>
                <option value="popular">最受欢迎</option>
                <option value="count">图片最多</option>
            </select>
        </div>

        <section class="categories" id="categoriesSection">
            <div class="category-card" onclick="showCategory('korea')">
                <span class="category-icon">🌸</span>
                <h3 class="category-title">韩系风格</h3>
                <p class="category-count" id="korea-count">加载中...</p>
            </div>
            <div class="category-card" onclick="showCategory('cosplay')">
                <span class="category-icon">🎭</span>
                <h3 class="category-title">角色扮演</h3>
                <p class="category-count" id="cosplay-count">加载中...</p>
            </div>
            <div class="category-card" onclick="showCategory('japan')">
                <span class="category-icon">🌺</span>
                <h3 class="category-title">日系清新</h3>
                <p class="category-count" id="japan-count">加载中...</p>
            </div>
            <div class="category-card" onclick="showCategory('gravure')">
                <span class="category-icon">📸</span>
                <h3 class="category-title">专业写真</h3>
                <p class="category-count" id="gravure-count">加载中...</p>
            </div>
            <div class="category-card" onclick="showCategory('chinese')">
                <span class="category-icon">🏮</span>
                <h3 class="category-title">中式古典</h3>
                <p class="category-count" id="chinese-count">加载中...</p>
            </div>
            <div class="category-card" onclick="showCategory('thailand')">
                <span class="category-icon">🌴</span>
                <h3 class="category-title">泰式风情</h3>
                <p class="category-count" id="thailand-count">加载中...</p>
            </div>
        </section>

        <div id="albumsContainer" style="display: none;">
            <section class="albums-grid" id="albumsSection">
                <div class="loading">正在加载图集...</div>
            </section>
        </div>
    </main>

    <footer class="footer">
        <div class="footer-content">
            <div class="footer-links">
                <a href="/about" class="footer-link">关于我们</a>
                <a href="/contact" class="footer-link">联系方式</a>
                <a href="/privacy" class="footer-link">隐私政策</a>
                <a href="/terms" class="footer-link">使用条款</a>
            </div>
            <p>&copy; 2025 春色写真馆. 所有权利保留.</p>
        </div>
    </footer>

    <!-- 移动端用户下拉菜单模态框 -->
    <div class="mobile-dropdown-modal" id="mobileDropdownModal">
        <div class="mobile-dropdown-content">
            <a href="/change-password" class="mobile-dropdown-item">修改密码</a>
            <a href="/logout" class="mobile-dropdown-item">退出</a>
        </div>
    </div>

    <script>
        let currentView = 'categories';
        let currentAlbums = [];
        const placeholderSvg = '{{ placeholder_svg }}';

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadCategoryCounts();

            // 初始化视图状态（默认为分类浏览）
            switchView('categories');

            // 检查URL参数
            const urlParams = new URLSearchParams(window.location.search);
            const category = urlParams.get('category');
            if (category) {
                document.getElementById('categoryFilter').value = category;
                switchView('albums');
                filterContent();
            }
        });

        // 加载分类统计
        async function loadCategoryCounts() {
            try {
                const response = await fetch('/api/stats');
                const stats = await response.json();
                
                Object.keys(stats).forEach(category => {
                    const element = document.getElementById(category + '-count');
                    if (element) {
                        element.textContent = `${stats[category].albums}个图集 · ${stats[category].images}张图片`;
                    }
                });
            } catch (error) {
                console.error('加载统计失败:', error);
            }
        }

        // 加载图集数据 - 支持分页
        let currentAlbumsPage = 1;
        let totalAlbumsPages = 1;

        async function loadAlbums(page = 1) {
            try {
                currentAlbumsPage = page;
                let category, sort;

                if (isSearchMode) {
                    // 搜索模式：使用搜索时的分类和当前排序
                    category = currentSearchCategory;
                    sort = document.getElementById('sortFilter').value;
                } else {
                    // 正常模式：使用过滤器的值
                    category = document.getElementById('categoryFilter').value;
                    sort = document.getElementById('sortFilter').value;
                }

                let url = '/api/albums';
                const params = new URLSearchParams();

                if (category) {
                    params.append('category', category);
                }
                if (sort) {
                    params.append('sort', sort);
                }
                // 保持搜索参数
                if (currentSearchQuery) {
                    params.append('search', currentSearchQuery);
                }
                params.append('page', page);
                params.append('limit', 16);

                url += '?' + params.toString();

                const response = await fetch(url);
                const data = await response.json();

                currentAlbums = data.albums;
                totalAlbumsPages = data.total_pages;
                displayAlbums();
                updateAlbumsPagination();

            } catch (error) {
                console.error('加载图集失败:', error);
                document.getElementById('albumsSection').innerHTML = '<div class="loading">加载失败，请刷新页面重试</div>';
            }
        }

        // 通用分页生成函数
        function generatePagination(currentPage, totalPages, onPageClick) {
            if (totalPages <= 1) return '';

            let paginationHTML = '<div class="pagination-wrapper"><div class="pagination-container">';

            // 上一页按钮
            const prevDisabled = currentPage <= 1;
            paginationHTML += `<button class="page-nav-btn" onclick="${onPageClick}(${currentPage - 1})" ${prevDisabled ? 'disabled' : ''}>上一页</button>`;

            // 页码逻辑
            const delta = 2; // 当前页前后显示的页数
            const range = [];
            const rangeWithDots = [];

            // 计算显示的页码范围
            for (let i = Math.max(2, currentPage - delta); i <= Math.min(totalPages - 1, currentPage + delta); i++) {
                range.push(i);
            }

            // 添加第一页
            if (currentPage - delta > 2) {
                rangeWithDots.push(1, '...');
            } else {
                rangeWithDots.push(1);
            }

            // 添加中间页码
            rangeWithDots.push(...range);

            // 添加最后一页
            if (currentPage + delta < totalPages - 1) {
                rangeWithDots.push('...', totalPages);
            } else if (totalPages > 1) {
                rangeWithDots.push(totalPages);
            }

            // 生成页码按钮
            rangeWithDots.forEach(page => {
                if (page === '...') {
                    paginationHTML += '<span class="page-ellipsis">...</span>';
                } else {
                    const isActive = page === currentPage;
                    const activeClass = isActive ? 'active' : '';
                    paginationHTML += `<button class="page-btn ${activeClass}" onclick="${onPageClick}(${page})">${page}</button>`;
                }
            });

            // 下一页按钮
            const nextDisabled = currentPage >= totalPages;
            paginationHTML += `<button class="page-nav-btn" onclick="${onPageClick}(${currentPage + 1})" ${nextDisabled ? 'disabled' : ''}>下一页</button>`;

            // 页面跳转功能
            if (totalPages > 10) {
                paginationHTML += `
                    <div class="page-jump-container">
                        <span class="page-jump-label">跳转到:</span>
                        <select class="page-jump-select" id="pageJumpSelect_${onPageClick}" onchange="jumpToPageFromSelect('${onPageClick}', this.value, ${totalPages})">
                            <option value="">选择页面</option>`;

                // 生成页面选项，对于大量页面使用分组
                if (totalPages <= 50) {
                    for (let i = 1; i <= totalPages; i++) {
                        paginationHTML += `<option value="${i}" ${i === currentPage ? 'selected' : ''}>第 ${i} 页</option>`;
                    }
                } else {
                    // 分组显示页面
                    const groupSize = 10;
                    for (let start = 1; start <= totalPages; start += groupSize) {
                        const end = Math.min(start + groupSize - 1, totalPages);
                        paginationHTML += `<optgroup label="第 ${start}-${end} 页">`;
                        for (let i = start; i <= end; i++) {
                            paginationHTML += `<option value="${i}" ${i === currentPage ? 'selected' : ''}>第 ${i} 页</option>`;
                        }
                        paginationHTML += `</optgroup>`;
                    }
                }

                paginationHTML += `
                        </select>
                        <input type="number" class="page-jump-input" id="pageJumpInput_${onPageClick}"
                               placeholder="页码" min="1" max="${totalPages}"
                               onkeypress="handlePageJumpKeypress(event, '${onPageClick}', ${totalPages})">
                        <button class="page-jump-btn" onclick="jumpToPageFromInput('${onPageClick}', ${totalPages})">跳转</button>
                    </div>`;
            }

            paginationHTML += '</div></div>';
            return paginationHTML;
        }

        // 更新图集分页控件
        function updateAlbumsPagination() {
            const albumsContainer = document.getElementById('albumsContainer');

            // 移除现有分页
            const existingPagination = albumsContainer.parentNode.querySelector('.pagination-wrapper');
            if (existingPagination) {
                existingPagination.remove();
            }

            // 添加新分页
            if (totalAlbumsPages > 1) {
                const paginationHTML = generatePagination(currentAlbumsPage, totalAlbumsPages, 'loadAlbums');
                albumsContainer.insertAdjacentHTML('afterend', paginationHTML);
            }
        }

        // 页面跳转功能
        function jumpToPageFromSelect(onPageClick, page, totalPages) {
            const pageNum = parseInt(page);
            if (pageNum && pageNum >= 1 && pageNum <= totalPages) {
                window[onPageClick](pageNum);
            }
        }

        function jumpToPageFromInput(onPageClick, totalPages) {
            const input = document.getElementById(`pageJumpInput_${onPageClick}`);
            const pageNum = parseInt(input.value);

            if (!pageNum || pageNum < 1 || pageNum > totalPages) {
                input.style.borderColor = '#ff6b6b';
                setTimeout(() => {
                    input.style.borderColor = '#ffb6c1';
                }, 1000);
                return;
            }

            input.value = '';
            window[onPageClick](pageNum);
        }

        function handlePageJumpKeypress(event, onPageClick, totalPages) {
            if (event.key === 'Enter') {
                jumpToPageFromInput(onPageClick, totalPages);
            }
        }

        // 显示图集
        function displayAlbums() {
            const section = document.getElementById('albumsSection');
            
            if (currentAlbums.length === 0) {
                section.innerHTML = '<div class="loading">没有找到相关图集</div>';
                return;
            }

            section.innerHTML = currentAlbums.map(album => `
                <div class="album-card" onclick="openAlbum('${album.category}', '${album.album}')">
                    <img src="${album.cover_image}" alt="${album.album}" class="album-cover" loading="lazy" onerror="this.src='${placeholderSvg}'">
                    <h3 class="album-title">${album.album}</h3>
                    <p class="album-count">${getCategoryName(album.category)} · ${album.image_count}张图片</p>
                </div>
            `).join('');
        }

        // 获取分类中文名
        function getCategoryName(category) {
            const names = {
                'korea': '韩系',
                'cosplay': '角色扮演',
                'japan': '日系',
                'gravure': '写真',
                'chinese': '中式',
                'thailand': '泰式'
            };
            return names[category] || category;
        }

        // 切换视图
        function switchView(view) {
            currentView = view;

            // 更新按钮状态
            document.getElementById('categoriesBtn').classList.toggle('active', view === 'categories');
            document.getElementById('albumsBtn').classList.toggle('active', view === 'albums');

            // 显示/隐藏对应区域
            document.getElementById('categoriesSection').style.display = view === 'categories' ? 'grid' : 'none';
            document.getElementById('albumsContainer').style.display = view === 'albums' ? 'block' : 'none';

            // 显示/隐藏过滤栏和分页控件
            const filterBar = document.querySelector('.filter-bar');
            const paginationWrapper = document.querySelector('.pagination-wrapper');

            if (view === 'categories') {
                // 分类浏览模式：隐藏过滤栏和分页控件
                if (filterBar) filterBar.style.display = 'none';
                if (paginationWrapper) paginationWrapper.style.display = 'none';
            } else {
                // 图集浏览模式：显示过滤栏和分页控件
                if (filterBar) filterBar.style.display = 'flex';
                if (paginationWrapper) paginationWrapper.style.display = 'block';
                // 更新过滤栏显示状态
                updateFilterBarForSearch();
            }

            // 如果切换到图集视图且还没加载数据，则加载
            if (view === 'albums' && currentAlbums.length === 0) {
                loadAlbums();
            }
        }

        // 过滤内容
        function filterContent() {
            if (currentView === 'albums') {
                if (isSearchMode) {
                    // 搜索模式：只重新加载，不清除搜索状态
                    currentAlbumsPage = 1; // 重置页码
                    loadAlbums(1);
                } else {
                    // 正常模式：清除搜索状态并重新加载
                    clearSearch();
                    currentAlbumsPage = 1; // 重置页码
                    loadAlbums(1);
                }
            }
        }

        // 搜索功能
        let currentSearchQuery = ''; // 存储当前搜索查询
        let currentSearchCategory = ''; // 存储当前搜索分类
        let isSearchMode = false; // 标记是否处于搜索模式

        async function performSearch() {
            const query = document.getElementById('searchInput').value.toLowerCase().trim();
            const category = document.getElementById('searchCategoryFilter').value;

            if (!query) {
                return;
            }

            // 存储搜索查询和分类
            currentSearchQuery = query;
            currentSearchCategory = category;
            isSearchMode = true;

            // 切换到图集视图进行搜索
            switchView('albums');

            // 隐藏分类过滤器，只显示排序过滤器
            updateFilterBarForSearch();

            try {
                let url = `/api/albums?search=${encodeURIComponent(query)}&page=1&limit=16`;
                if (category) {
                    url += `&category=${encodeURIComponent(category)}`;
                }

                const response = await fetch(url);
                const data = await response.json();
                currentAlbums = data.albums || data; // 兼容新旧API格式
                totalAlbumsPages = data.total_pages || 1;
                currentAlbumsPage = 1;
                displayAlbums();
                updateAlbumsPagination();
            } catch (error) {
                console.error('搜索失败:', error);
                document.getElementById('albumsSection').innerHTML = '<div class="loading">搜索失败，请重试</div>';
            }
        }

        // 更新过滤栏显示状态
        function updateFilterBarForSearch() {
            const filterBar = document.querySelector('.filter-bar');
            const categoryFilter = document.getElementById('categoryFilter');
            const sortFilter = document.getElementById('sortFilter');

            if (isSearchMode) {
                // 搜索模式：隐藏分类过滤器，只显示排序过滤器
                categoryFilter.style.display = 'none';
                sortFilter.style.display = 'block';
            } else {
                // 正常模式：显示所有过滤器
                categoryFilter.style.display = 'block';
                sortFilter.style.display = 'block';
            }
        }

        // 清除搜索状态
        function clearSearch() {
            currentSearchQuery = '';
            currentSearchCategory = '';
            isSearchMode = false;
            document.getElementById('searchInput').value = '';
            document.getElementById('searchCategoryFilter').value = '';
            updateFilterBarForSearch();
        }

        // 显示分类
        function showCategory(category) {
            clearSearch(); // 清除搜索状态
            document.getElementById('categoryFilter').value = category;
            switchView('albums');
            filterContent();
        }

        // 打开图集
        function openAlbum(category, album) {
            window.location.href = `/album/${category}/${encodeURIComponent(album)}`;
        }

        // 键盘事件
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && document.getElementById('searchInput') === document.activeElement) {
                performSearch();
            }
        });



        // User dropdown functionality - 重新设计
        function toggleUserDropdown() {
            if (window.innerWidth <= 768) {
                // 移动端使用模态框
                const mobileModal = document.getElementById('mobileDropdownModal');
                if (mobileModal) {
                    mobileModal.classList.toggle('show');
                }
            } else {
                // 桌面端使用下拉菜单
                const dropdown = document.getElementById('userDropdown');
                if (dropdown) {
                    dropdown.classList.toggle('show');
                }
            }
        }

        // 关闭下拉菜单
        function closeDropdowns() {
            const dropdown = document.getElementById('userDropdown');
            const mobileModal = document.getElementById('mobileDropdownModal');

            if (dropdown) dropdown.classList.remove('show');
            if (mobileModal) mobileModal.classList.remove('show');
        }

        // 点击外部关闭
        document.addEventListener('click', function(event) {
            const userInfo = document.querySelector('.user-dropdown');
            const mobileModal = document.getElementById('mobileDropdownModal');

            // 如果点击的不是用户信息区域
            if (userInfo && !userInfo.contains(event.target)) {
                closeDropdowns();
            }

            // 如果点击的是移动端模态框背景
            if (event.target === mobileModal) {
                closeDropdowns();
            }
        });

        // 触摸事件支持
        document.addEventListener('touchstart', function(event) {
            const userInfo = document.querySelector('.user-dropdown');
            const mobileModal = document.getElementById('mobileDropdownModal');

            if (userInfo && !userInfo.contains(event.target) && event.target !== mobileModal) {
                closeDropdowns();
            }
        });
    </script>
</body>
</html>'''

# 收藏页面模板
# BUG FIX: favorites.js logic updated to work with server-side pagination
FAVORITES_TEMPLATE = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的收藏 - 春色写真馆</title>
    <link rel="icon" type="image/svg+xml" href="/favicon.ico">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* 移除所有列表标记 */
        ul, ol { list-style: none; }
        li::marker { display: none; }

        body {
            font-family: 'PingFang SC', 'Hiragino Sans GB', 'Noto Sans CJK SC', 'Source Han Sans SC', 'Microsoft YaHei', '微软雅黑', sans-serif;
            background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 25%, #f0fff4 50%, #fff8dc 75%, #fdf5e6 100%);
            min-height: 100vh;
            color: #5d4e37;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            padding: 1rem 0;
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
            box-shadow: 0 2px 20px rgba(255, 182, 193, 0.3);
        }
        
        .nav {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
        }
        
        .logo {
            font-size: 2rem;
            font-weight: bold;
            background: linear-gradient(45deg, #ff69b4, #ffa500, #98fb98);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        .nav-links {
            display: flex;
            gap: 2rem;
            align-items: center;
        }
        
        .nav-link {
            color: #8b4513;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .nav-link:hover, .nav-link.active {
            background: linear-gradient(45deg, rgba(255, 182, 193, 0.3), rgba(255, 218, 185, 0.3));
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 182, 193, 0.4);
        }
        
        .user-info {
            display: flex;
            align-items: center;
            justify-content: center; /* 水平居中 */
            gap: 0.3rem;
            max-width: 120px; /* 减小整体宽度 */
            flex-shrink: 0; /* 防止压缩 */
            position: relative;
        }

        .user-info .avatar {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            flex-shrink: 0; /* 头像不压缩 */
        }

        .user-info span {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 50px; /* 进一步减小昵称宽度 */
            font-size: 0.8rem;
            cursor: pointer;
            position: relative;
            display: inline-block;
            vertical-align: middle;
            flex-shrink: 1; /* 允许文字压缩 */
            min-width: 0; /* 允许收缩到0 */
        }

        /* User dropdown menu - 重新设计 */
        .user-dropdown {
            position: relative;
            display: inline-block;
        }

        .user-dropdown-content {
            display: none;
            position: absolute;
            right: 0;
            top: 100%;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            min-width: 150px;
            box-shadow: 0 8px 25px rgba(255, 182, 193, 0.3);
            border-radius: 10px;
            z-index: 1001;
            border: 1px solid rgba(255, 182, 193, 0.2);
            overflow: hidden;
            margin-top: 5px;
        }

        .user-dropdown-content.show {
            display: block;
        }

        .dropdown-item {
            display: block;
            padding: 0.8rem 1rem;
            color: #8b4513;
            text-decoration: none;
            transition: all 0.3s ease;
            border-bottom: 1px solid rgba(255, 182, 193, 0.1);
        }

        .dropdown-item:last-child {
            border-bottom: none;
        }

        .dropdown-item:hover {
            background: linear-gradient(45deg, #ff69b4, #ffa500);
            color: white;
            transform: translateX(5px);
        }

        /* 移动端模态框样式下拉菜单 */
        .mobile-dropdown-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 9999;
            justify-content: center;
            align-items: center;
        }

        .mobile-dropdown-modal.show {
            display: flex;
        }

        .mobile-dropdown-content {
            background: white;
            border-radius: 15px;
            padding: 1rem;
            min-width: 200px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            animation: slideUp 0.3s ease;
        }

        @keyframes slideUp {
            from {
                transform: translateY(50px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .mobile-dropdown-item {
            display: block;
            padding: 1rem;
            color: #8b4513;
            text-decoration: none;
            text-align: center;
            border-bottom: 1px solid rgba(255, 182, 193, 0.2);
            transition: all 0.3s ease;
        }

        .mobile-dropdown-item:last-child {
            border-bottom: none;
        }

        .mobile-dropdown-item:hover {
            background: linear-gradient(45deg, #ff69b4, #ffa500);
            color: white;
        }
        
        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 2px solid #ffb6c1;
            object-fit: cover;
        }
        
        .main-content {
            margin-top: 100px;
            padding: 2rem;
            max-width: 1400px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .page-header {
            text-align: center;
            padding: 3rem 0;
            background: linear-gradient(135deg, rgba(255, 182, 193, 0.1), rgba(255, 218, 185, 0.1));
            border-radius: 30px;
            margin-bottom: 3rem;
            position: relative;
            overflow: hidden;
        }
        
        .page-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 182, 193, 0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        
        .page-title {
            font-size: 3rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #ff69b4, #ffa500, #98fb98, #dda0dd);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            position: relative;
            z-index: 1;
        }
        
        .page-subtitle {
            font-size: 1.2rem;
            color: #8b4513;
            position: relative;
            z-index: 1;
        }
        
        .favorites-stats {
            background: rgba(255, 255, 255, 0.9);
            padding: 2rem;
            border-radius: 20px;
            margin-bottom: 3rem;
            text-align: center;
            box-shadow: 0 4px 15px rgba(255, 182, 193, 0.2);
        }
        
        .stats-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: rgba(255, 240, 245, 0.5);
            border-radius: 15px;
            border: 1px solid rgba(255, 182, 193, 0.3);
        }

        .stats-section:last-child {
            margin-bottom: 0;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .stat-item {
            padding: 1rem;
            background: linear-gradient(135deg, rgba(255, 182, 193, 0.1), rgba(255, 218, 185, 0.1));
            border-radius: 15px;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #ff69b4;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #8b4513;
            font-size: 0.9rem;
        }
        
        .filter-bar {
            background: rgba(255, 255, 255, 0.9);
            padding: 1rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 1rem;
            box-shadow: 0 4px 15px rgba(255, 182, 193, 0.2);
        }

        .filter-left {
            display: flex;
            align-items: center;
            gap: 1.5rem;
            flex-wrap: wrap;
        }

        .filter-select {
            padding: 0.5rem 1rem;
            border: 1px solid #ffb6c1;
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.9);
            color: #8b4513;
        }
        
        .clear-all-btn {
            padding: 0.5rem 1rem;
            background: linear-gradient(45deg, #ff6b6b, #ffa500);
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .clear-all-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
        }
        
        .gallery-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }

        .gallery-grid.large {
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 2rem;
        }

        .gallery-grid.small {
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 1rem;
        }

        .image-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            overflow: hidden;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            box-shadow: 0 4px 15px rgba(255, 182, 193, 0.2);
        }

        .image-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(255, 182, 193, 0.4);
        }

        .image-card img {
            width: 100%;
            height: 200px;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .gallery-grid.large .image-card img {
            height: 250px;
        }

        .gallery-grid.small .image-card img {
            height: 150px;
        }

        .image-card:hover img {
            transform: scale(1.05);
        }

        .image-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.7) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
            display: flex;
            align-items: flex-end;
            padding: 1rem;
        }

        .image-card:hover .image-overlay {
            opacity: 1;
        }

        .image-actions {
            display: flex;
            gap: 0.5rem;
        }

        .action-btn {
            background: rgba(255, 255, 255, 0.9);
            border: none;
            padding: 0.5rem;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .action-btn:hover {
            background: #ff69b4;
            color: white;
            transform: scale(1.1);
        }

        .action-btn.favorited {
            background: #ff69b4;
            color: white;
        }

        .gallery-controls {
            background: rgba(255, 255, 255, 0.9);
            padding: 1rem 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .view-controls {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .view-btn {
            padding: 0.5rem 1rem;
            border: 1px solid #ffb6c1;
            background: rgba(255, 255, 255, 0.9);
            color: #8b4513;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .view-btn.active, .view-btn:hover {
            background: linear-gradient(45deg, #ff69b4, #ffa500);
            color: white;
            transform: translateY(-2px);
        }
        
        .image-info {
            padding: 1rem;
        }
        
        .image-title {
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: #8b4513;
            cursor: pointer;
        }
        
        .image-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.9rem;
            color: #cd853f;
            margin-bottom: 1rem;
        }
        
        .favorited-time {
            font-size: 0.8rem;
            color: #999;
            margin-bottom: 1rem;
        }
        
        .card-actions {
            display: flex;
            gap: 0.5rem;
        }
        
        .btn-small {
            padding: 0.4rem 0.8rem;
            border: none;
            border-radius: 15px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.3rem;
        }
        
        .btn-remove {
            background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
            color: white;
        }
        
        .btn-view {
            background: linear-gradient(45deg, #98fb98, #87ceeb);
            color: #2f4f4f;
        }
        
        .btn-small:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }
        
        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 20px;
            margin: 2rem 0;
            /* BUG FIX: Ensure it spans the whole grid width */
            grid-column: 1 / -1;
        }
        
        .empty-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
        
        .empty-title {
            font-size: 1.5rem;
            color: #8b4513;
            margin-bottom: 1rem;
        }
        
        .empty-text {
            color: #cd853f;
            margin-bottom: 2rem;
        }
        
        .btn-primary {
            padding: 1rem 2rem;
            background: linear-gradient(45deg, #ff69b4, #ffa500);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 105, 180, 0.4);
        }
        
        .loading {
            text-align: center;
            padding: 2rem;
            font-size: 1.2rem;
            color: #8b4513;
            /* BUG FIX: Ensure it spans the whole grid width */
            grid-column: 1 / -1;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-top: 3rem;
        }
        
        .page-btn {
            padding: 0.8rem 1.2rem;
            border: 1px solid #ffb6c1;
            background: rgba(255, 255, 255, 0.9);
            color: #8b4513;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .page-btn:hover, .page-btn.active {
            background: linear-gradient(45deg, #ff69b4, #ffa500);
            color: white;
            transform: translateY(-2px);
        }
        
        .page-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
            border-color: #ddd;
        }

        /* 收藏标签样式 - 与主页view-toggle一致 */
        .favorites-tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }

        /* 视图模式切换样式 - 在收藏页面模板中定义 */
        .view-mode-toggle {
            display: flex;
            gap: 0.5rem;
        }

        .view-mode-btn {
            padding: 0.5rem 1rem;
            background: rgba(255, 255, 255, 0.9);
            color: #8b4513;
            border: 1px solid #ffb6c1;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            white-space: nowrap;
        }

        .view-mode-btn:hover,
        .view-mode-btn.active {
            background: linear-gradient(45deg, #ff69b4, #ffa500);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 105, 180, 0.4);
        }

        /* 图片分组样式 */
        .album-group {
            margin-bottom: 3rem;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 4px 15px rgba(255, 182, 193, 0.2);
        }

        .album-group-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid rgba(255, 182, 193, 0.2);
        }

        .album-group-title {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .album-name {
            font-size: 1.5rem;
            font-weight: bold;
            color: #8b4513;
        }

        .album-category {
            font-size: 1rem;
            color: #ff69b4;
            font-weight: normal;
        }

        .album-count {
            font-size: 0.9rem;
            color: #666;
            font-weight: normal;
        }

        .album-group-actions {
            display: flex;
            gap: 1rem;
        }

        .album-group-images {
            margin-top: 1rem;
        }

        /* 分页样式 */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 1rem;
            margin: 2rem 0;
        }

        .pagination-btn {
            padding: 0.8rem 1.5rem;
            border: 1px solid #ffb6c1;
            background: rgba(255, 255, 255, 0.9);
            color: #8b4513;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
        }

        .pagination-btn:hover:not(:disabled) {
            background: linear-gradient(45deg, #ff69b4, #ffa500);
            color: white;
            transform: translateY(-2px);
        }

        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .pagination-info {
            font-size: 1rem;
            color: #8b4513;
            font-weight: 500;
        }

        .view-toggle {
            display: flex;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 25px;
            padding: 0.3rem;
            box-shadow: 0 10px 30px rgba(255, 182, 193, 0.2);
            border: 2px solid rgba(255, 182, 193, 0.3);
        }

        .toggle-btn {
            padding: 0.8rem 1.5rem;
            border: none;
            background: transparent;
            color: #8b4513;
            border-radius: 20px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
            white-space: nowrap;
        }

        .toggle-btn:hover {
            background: rgba(255, 182, 193, 0.1);
        }

        .toggle-btn.active {
            background: linear-gradient(45deg, #ffb6c1, #ffc0cb);
            color: white;
            box-shadow: 0 3px 10px rgba(255, 105, 180, 0.3);
        }

        /* 图集网格样式 - 与主页一致 */
        .albums-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 2rem;
            padding: 2rem 0;
        }

        .album-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            overflow: hidden;
            transition: all 0.3s ease;
            cursor: pointer;
            box-shadow: 0 10px 30px rgba(255, 182, 193, 0.2);
            border: 2px solid rgba(255, 182, 193, 0.1);
        }

        .album-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(255, 105, 180, 0.3);
            border-color: rgba(255, 105, 180, 0.3);
        }

        .album-cover {
            width: 100%;
            height: 200px;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .album-card:hover .album-cover {
            transform: scale(1.05);
        }

        .album-title {
            padding: 1rem 1.5rem 0.5rem;
            font-size: 1.1rem;
            font-weight: 600;
            color: #8b4513;
            margin: 0;
            line-height: 1.4;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .album-count {
            padding: 0 1.5rem 1.5rem;
            color: #666;
            font-size: 0.9rem;
            margin: 0;
        }

        /* 图集取消收藏按钮 */
        .album-unfavorite-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 32px;
            height: 32px;
            background: rgba(0, 0, 0, 0.6);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 14px;
            color: white;
            opacity: 0;
            transition: all 0.3s ease;
            z-index: 10;
            backdrop-filter: blur(5px);
        }

        .album-card:hover .album-unfavorite-btn {
            opacity: 1;
        }

        .album-unfavorite-btn:hover {
            background: rgba(255, 69, 58, 0.8);
            transform: scale(1.1);
        }

        /* 分页样式 - 与主页完全一致 */
        .pagination-wrapper {
            width: 100%;
            display: flex;
            justify-content: center;
            margin: 2rem 0;
        }

        .pagination-container {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem 1.5rem;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 25px;
            box-shadow: 0 4px 15px rgba(255, 182, 193, 0.2);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 182, 193, 0.1);
            flex-wrap: wrap;
        }

        .page-btn, .page-nav-btn {
            padding: 0.5rem 0.8rem;
            border: 1px solid #ffb6c1;
            background: rgba(255, 255, 255, 0.9);
            color: #8b4513;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            min-width: 40px;
            text-align: center;
        }

        .page-btn:hover, .page-btn.active {
            background: linear-gradient(45deg, #ff69b4, #ffa500);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 105, 180, 0.4);
            border-color: transparent;
        }

        .page-nav-btn:hover:not(:disabled) {
            background: linear-gradient(45deg, #98fb98, #87ceeb);
            color: #2f4f4f;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(152, 251, 152, 0.4);
            border-color: transparent;
        }

        .page-nav-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
            border-color: #ddd;
        }

        .page-ellipsis {
            padding: 0.5rem 0.3rem;
            color: #8b4513;
            font-weight: bold;
        }

        .page-jump-container {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-left: 1rem;
            padding-left: 1rem;
            border-left: 1px solid rgba(255, 182, 193, 0.3);
        }

        .page-jump-label {
            color: #8b4513;
            font-size: 0.9rem;
            white-space: nowrap;
        }

        .page-jump-select, .page-jump-input {
            padding: 0.4rem 0.6rem;
            border: 1px solid #ffb6c1;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.9);
            color: #8b4513;
            font-size: 0.8rem;
            transition: all 0.3s ease;
        }

        .page-jump-select:focus, .page-jump-input:focus {
            outline: none;
            border-color: #ff69b4;
            box-shadow: 0 0 5px rgba(255, 105, 180, 0.3);
        }

        .page-jump-input {
            width: 60px;
        }

        .page-jump-btn {
            padding: 0.4rem 0.8rem;
            background: linear-gradient(45deg, #98fb98, #87ceeb);
            color: #2f4f4f;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.3s ease;
            white-space: nowrap;
        }

        .page-jump-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 10px rgba(152, 251, 152, 0.4);
        }



        /* 图片查看器模态框 - 与相册页面一致 */
        .modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.95);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            position: relative;
            margin: 0 auto;
            max-width: 95%;
            max-height: 95%;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100vh;
            padding: 2% 0;
        }

        .modal-image {
            max-width: 100%;
            max-height: 90vh;
            object-fit: contain;
            border-radius: 10px;
        }

        .modal-nav {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.9);
            border: none;
            padding: 1rem;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.5rem;
            transition: all 0.3s ease;
            z-index: 2001;
        }

        .modal-nav:hover {
            background: #ff69b4;
            color: white;
            transform: translateY(-50%) scale(1.1);
        }

        .modal-prev {
            left: 20px;
        }

        .modal-next {
            right: 20px;
        }

        .modal-close {
            position: absolute;
            top: 20px;
            right: 30px;
            color: #fff;
            font-size: 40px;
            font-weight: bold;
            cursor: pointer;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 50%;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            z-index: 2001;
        }

        .modal-close:hover {
            background: rgba(255, 105, 180, 0.8);
            transform: scale(1.1);
        }

        .modal-info {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 1rem 2rem;
            border-radius: 25px;
            text-align: center;
            z-index: 2001;
        }

        @media (max-width: 768px) {
            .nav {
                flex-direction: row;
                gap: 0.5rem;
                padding: 0 0.5rem;
                align-items: center;
                justify-content: space-between;
            }

            .logo {
                font-size: 1.2rem;
                flex-shrink: 0;
            }

            .nav-links {
                gap: 0.3rem;
                flex-wrap: nowrap;
                justify-content: flex-end;
                align-items: center;
                overflow-x: auto;
                flex: 1;
                min-width: 0;
            }

            .nav-link {
                font-size: 0.8rem;
                padding: 0.3rem 0.5rem;
                white-space: nowrap;
                flex-shrink: 0;
            }

            .user-info {
                flex-direction: row;
                gap: 0.2rem;
                align-items: center;
                justify-content: center; /* 移动端也居中 */
                max-width: 80px; /* 进一步减小移动端宽度 */
                flex-shrink: 0;
            }

            .user-info .avatar {
                width: 20px;
                height: 20px;
                flex-shrink: 0; /* 头像不压缩 */
            }

            .user-info span {
                max-width: 35px; /* 大幅减小移动端文字宽度 */
                font-size: 0.65rem;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                display: inline-block;
                vertical-align: middle;
                flex-shrink: 1; /* 允许文字压缩 */
                min-width: 0; /* 允许收缩到0 */
            }

            /* 移动端隐藏桌面版下拉菜单，使用模态框版本 */
            .user-dropdown-content {
                display: none !important;
            }

            .page-title {
                font-size: 2.5rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .gallery-grid {
                grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
                gap: 1rem;
            }

            .filter-bar {
                flex-direction: column;
                align-items: stretch;
                gap: 1rem;
            }

            .filter-left {
                flex-direction: column;
                align-items: stretch;
                gap: 1rem;
            }

            .view-mode-toggle {
                justify-content: center;
                gap: 0.3rem;
            }

            .view-mode-btn {
                padding: 0.4rem 0.8rem;
                font-size: 0.8rem;
            }



            .view-toggle {
                flex-direction: column;
                gap: 0.3rem;
                padding: 0.5rem;
            }

            .toggle-btn {
                padding: 0.6rem 1rem;
                font-size: 0.9rem;
            }

            .albums-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 1rem;
            }

            /* 移动端分页样式 */
            .pagination-wrapper {
                margin: 1.5rem 0;
            }

            .pagination-container {
                flex-direction: column;
                gap: 1rem;
                padding: 1rem;
            }

            .page-btn, .page-nav-btn {
                padding: 0.4rem 0.6rem;
                font-size: 0.8rem;
                min-width: 35px;
            }

            .page-nav-btn {
                padding: 0.4rem 0.8rem;
            }

            .page-jump-container {
                margin-left: 0;
                padding-left: 0;
                border-left: none;
                border-top: 1px solid rgba(255, 182, 193, 0.3);
                padding-top: 1rem;
                flex-wrap: wrap;
                justify-content: center;
            }

            .page-jump-label {
                width: 100%;
                text-align: center;
                margin-bottom: 0.5rem;
            }

            /* 移动端图集取消收藏按钮 */
            .album-unfavorite-btn {
                opacity: 1; /* 移动端始终显示 */
                width: 28px;
                height: 28px;
                font-size: 12px;
            }

            /* 手机端模态框样式 - 与相册页面一致 */
            .modal-content {
                height: 100vh;
                padding: 5% 0;
                align-items: center;
                justify-content: center;
            }

            .modal-image {
                max-width: 95%;
                max-height: 85vh;
                object-fit: contain;
                border-radius: 10px;
            }

            /* 手机端模态框导航按钮 */
            .modal-nav {
                padding: 0.8rem;
                font-size: 1.2rem;
                width: 50px;
                height: 50px;
                display: flex;
                align-items: center;
                justify-content: center;
                background: rgba(255, 255, 255, 0.9);
                backdrop-filter: blur(5px);
            }

            .modal-prev {
                left: 10px;
            }

            .modal-next {
                right: 10px;
            }

            .modal-close {
                top: auto;
                bottom: 20px;
                right: 15px;
                width: 50px;
                height: 50px;
                font-size: 30px;
                background: rgba(0, 0, 0, 0.7);
                backdrop-filter: blur(5px);
            }

            .modal-favorite {
                position: absolute;
                bottom: 20px;
                right: 75px; /* Position next to close button */
                width: 50px;
                height: 50px;
                font-size: 24px;
                background: rgba(0, 0, 0, 0.7);
                backdrop-filter: blur(5px);
                border: none;
                border-radius: 50%;
                color: white;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.3s ease;
                z-index: 2001;
            }

            .modal-favorite:hover {
                background: rgba(255, 105, 180, 0.8);
                transform: scale(1.1);
            }

            .modal-favorite.favorited {
                background: rgba(255, 105, 180, 0.8);
                color: #ff1493;
            }

            .modal-info {
                bottom: 10px;
                padding: 0.8rem 1.5rem;
                font-size: 0.9rem;
                background: rgba(0, 0, 0, 0.8);
                backdrop-filter: blur(5px);
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <nav class="nav">
            <a href="/" class="logo" style="text-decoration: none; color: inherit;">🌸 春色写真馆</a>
            <div class="nav-links">
                <a href="/" class="nav-link">首页</a>
                <a href="/gallery" class="nav-link">图库</a>
                <a href="/favorites" class="nav-link active">收藏</a>
                <a href="/admin" class="nav-link">管理</a>
                <div class="user-info user-dropdown">
                    <img src="{{ session.avatar or default_avatar }}" alt="头像" class="avatar">
                    <span onclick="toggleUserDropdown()" title="点击查看选项">{{ session.username }}</span>
                    <div class="user-dropdown-content" id="userDropdown">
                        <a href="/change-password" class="dropdown-item">修改密码</a>
                        <a href="/logout" class="dropdown-item">退出</a>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <main class="main-content">
        <section class="page-header">
            <h1 class="page-title">❤️ 我的收藏</h1>
            <p class="page-subtitle">珍藏美好瞬间，记录心动时刻</p>
        </section>

        <div class="favorites-stats">
            <h3 style="color: #8b4513; margin-bottom: 1.5rem;">📊 收藏统计</h3>

            <!-- 总览统计 -->
            <div class="stats-section">
                <h4 style="color: #8b4513; margin-bottom: 1rem; font-size: 1.1rem;">📈 总览</h4>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number" id="totalFavorites">0</div>
                        <div class="stat-label">总收藏数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="totalCategories">0</div>
                        <div class="stat-label">涉及分类</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="totalRecent">0</div>
                        <div class="stat-label">本周新增</div>
                    </div>
                </div>
            </div>

            <!-- 图片收藏统计 -->
            <div class="stats-section">
                <h4 style="color: #8b4513; margin-bottom: 1rem; font-size: 1.1rem;">📸 图片收藏</h4>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number" id="imagesFavorites">0</div>
                        <div class="stat-label">收藏图片</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="imagesCategories">0</div>
                        <div class="stat-label">涉及分类</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="imagesAlbums">0</div>
                        <div class="stat-label">涉及图集</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="imagesRecent">0</div>
                        <div class="stat-label">本周新增</div>
                    </div>
                </div>
            </div>

            <!-- 图集收藏统计 -->
            <div class="stats-section">
                <h4 style="color: #8b4513; margin-bottom: 1rem; font-size: 1.1rem;">📂 图集收藏</h4>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number" id="albumsFavorites">0</div>
                        <div class="stat-label">收藏图集</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="albumsCategories">0</div>
                        <div class="stat-label">涉及分类</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="albumsRecent">0</div>
                        <div class="stat-label">本周新增</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 收藏类型选择标签 -->
        <div class="favorites-tabs">
            <div class="view-toggle">
                <button class="toggle-btn active" onclick="switchFavoritesTab('albums')" data-tab="albums" id="albumsTabBtn">
                    📂 收藏图集
                </button>
                <button class="toggle-btn" onclick="switchFavoritesTab('images')" data-tab="images" id="imagesTabBtn">
                    📸 收藏图片
                </button>
            </div>
        </div>

        <!-- 图集收藏过滤栏 -->
        <div class="filter-bar" id="albumsFilterBar">
            <div class="filter-left">
                <select class="filter-select" id="albumCategoryFilter" onchange="filterFavoritedAlbums()">
                    <option value="">所有分类</option>
                    <option value="korea">韩系</option>
                    <option value="cosplay">角色扮演</option>
                    <option value="japan">日系</option>
                    <option value="gravure">写真</option>
                    <option value="chinese">中式</option>
                    <option value="thailand">泰式</option>
                </select>
                <select class="filter-select" id="albumSortFilter" onchange="filterFavoritedAlbums()">
                    <option value="newest">最新收藏</option>
                    <option value="oldest">最早收藏</option>
                    <option value="name">按名称</option>
                </select>
            </div>
            <button class="clear-all-btn" onclick="clearAllAlbumFavorites()">🗑️ 清空图集收藏</button>
        </div>

        <!-- 图片收藏过滤栏 -->
        <div class="filter-bar" id="imagesFilterBar" style="display: none;">
            <div class="filter-left">
                <!-- 图片收藏模式切换 -->
                <div class="view-mode-toggle">
                    <button class="view-mode-btn view-mode-btn-alt active" onclick="switchImagesMode('all')" data-mode="all">
                        🖼️ 全部显示
                    </button>
                    <button class="view-mode-btn view-mode-btn-alt" onclick="switchImagesMode('grouped')" data-mode="grouped">
                        📁 按图集分组
                    </button>
                </div>
                <select class="filter-select" id="categoryFilter" onchange="filterFavorites()">
                    <option value="">所有分类</option>
                    <option value="korea">韩系</option>
                    <option value="cosplay">角色扮演</option>
                    <option value="japan">日系</option>
                    <option value="gravure">写真</option>
                    <option value="chinese">中式</option>
                    <option value="thailand">泰式</option>
                </select>
                <select class="filter-select" id="sortFilter" onchange="filterFavorites()">
                    <option value="newest">最新收藏</option>
                    <option value="oldest">最早收藏</option>
                </select>
            </div>
            <button class="clear-all-btn" onclick="clearAllFavorites()">🗑️ 清空图片收藏</button>
        </div>

        <!-- 图片视图控制 - 与相册页面一致 -->
        <div class="gallery-controls" id="galleryControls" style="display: none;">
            <div class="view-controls">
                <button class="view-btn active" onclick="setViewSize('medium')" data-size="medium">中等</button>
                <button class="view-btn" onclick="setViewSize('large')" data-size="large">大图</button>
                <button class="view-btn" onclick="setViewSize('small')" data-size="small">小图</button>
            </div>
        </div>

        <!-- 图集收藏容器 -->
        <div id="albumsContainer">
            <section class="albums-grid" id="favoritesGrid">
                <div class="loading">正在加载收藏内容...</div>
            </section>
        </div>

        <!-- 图片收藏容器 -->
        <div id="imagesContainer" style="display: none;">
            <!-- 分页信息 -->
            <div class="pagination-info" id="imagesPaginationInfo" style="display: none;">
                <span id="imagesCurrentInfo">第 1 页，共 0 张图片</span>
            </div>

            <!-- 图片网格 -->
            <section class="gallery-grid" id="imagesGrid">
                <div class="loading">正在加载收藏图片...</div>
            </section>

            <!-- 分页控件 - 使用主页的完整分页结构 -->
            <div id="imagesPagination" style="display: none;">
                <!-- 分页内容将由 generatePagination 函数动态生成 -->
            </div>
        </div>



        <!-- 图片查看器模态框 - 收藏页面专用版本 -->
        <div id="imageModal" class="modal">
            <div class="modal-content">
                <span class="modal-close" onclick="closeModal()">&times;</span>
                <!-- 收藏页面不需要收藏按钮，因为这些图片已经是收藏的 -->
                <button class="modal-nav modal-prev" onclick="prevImage()">‹</button>
                <img class="modal-image" id="modalImage" src="{{ placeholder_svg }}" alt="">
                <button class="modal-nav modal-next" onclick="nextImage()">›</button>
                <div class="modal-info">
                    <div id="modalInfo">1 / 10</div>
                </div>
            </div>
        </div>
    </main>

    <!-- BUG FIX: 全面重写收藏页面的 JS 逻辑以支持后端分页和双视图模式 -->
    <script>
        let currentPage = 1;
        let itemsPerPage = 12;
        let totalFavorites = 0;
        let currentFavoritesTab = 'albums'; // 'albums' 或 'images'
        let currentImagesMode = 'all'; // 'all' 或 'grouped'
        let currentImages = []; // 当前显示的图片数组
        let currentModalIndex = 0; // 当前模态框显示的图片索引
        let userFavorites = new Set(); // 存储用户收藏的图片ID
        const placeholderSvg = '{{ placeholder_svg }}';

        // 加载用户收藏状态
        async function loadUserFavorites() {
            try {
                const response = await fetch('/api/user-favorites');
                const favorites = await response.json();
                userFavorites = new Set(favorites.map(f => f.image_id.toString()));
                console.log(`Loaded ${userFavorites.size} user favorites`);
            } catch (error) {
                console.error('加载收藏状态失败:', error);
            }
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 加载用户收藏状态
            {% if session.user_id %}
            loadUserFavorites();
            {% endif %}

            switchFavoritesTab('albums'); // 默认显示图集收藏
            loadFavoritesStats();
        });

        // 切换收藏标签
        function switchFavoritesTab(tab) {
            currentFavoritesTab = tab;
            currentPage = 1;

            // 清除图集上下文
            window.currentFavoritedAlbum = null;

            // 更新标签按钮状态
            document.querySelectorAll('.toggle-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-tab="${tab}"]`).classList.add('active');

            // 显示/隐藏对应的过滤栏和控制栏
            const albumsFilterBar = document.getElementById('albumsFilterBar');
            const imagesFilterBar = document.getElementById('imagesFilterBar');
            const galleryControls = document.getElementById('galleryControls');
            const albumsContainer = document.getElementById('albumsContainer');
            const imagesContainer = document.getElementById('imagesContainer');

            if (tab === 'albums') {
                // 显示图集收藏
                albumsFilterBar.style.display = 'flex';
                imagesFilterBar.style.display = 'none';
                galleryControls.style.display = 'none';
                albumsContainer.style.display = 'block';
                imagesContainer.style.display = 'none';
                loadFavoritedAlbums();
            } else {
                // 显示图片收藏
                albumsFilterBar.style.display = 'none';
                imagesFilterBar.style.display = 'flex';
                galleryControls.style.display = 'block';
                albumsContainer.style.display = 'none';
                imagesContainer.style.display = 'block';

                if (currentImagesMode === 'all') {
                    loadFavoriteImages();
                } else {
                    loadFavoriteImagesGrouped();
                }
            }
        }

        // 切换图片收藏模式
        function switchImagesMode(mode) {
            currentImagesMode = mode;
            currentPage = 1;

            // 清除图集上下文
            window.currentFavoritedAlbum = null;

            // 更新模式按钮状态
            document.querySelectorAll('.view-mode-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-mode="${mode}"]`).classList.add('active');

            // 加载对应数据
            if (mode === 'all') {
                loadFavoriteImages();
            } else {
                loadFavoriteImagesGrouped();
            }
        }

        // 加载收藏数据 (现在支持分页)
        async function loadFavorites(page = 1) {
            currentPage = page;
            const grid = document.getElementById('favoritesGrid');
            grid.innerHTML = '<div class="loading">正在加载收藏内容...</div>';

            try {
                const category = document.getElementById('categoryFilter').value;
                const sort = document.getElementById('sortFilter').value;
                
                const params = new URLSearchParams({
                    page: currentPage,
                    limit: itemsPerPage,
                    sort: sort
                });
                if (category) {
                    params.append('category', category);
                }

                const response = await fetch(`/api/favorites?${params.toString()}`);
                const data = await response.json();
                
                totalFavorites = data.total;
                displayFavorites(data.favorites);
                updatePagination();

            } catch (error) {
                console.error('加载收藏失败:', error);
                grid.innerHTML = '<div class="loading">加载失败，请刷新页面重试</div>';
            }
        }

        // 加载收藏图片 (全部显示模式)
        async function loadFavoriteImages(page = 1) {
            currentPage = page;
            const grid = document.getElementById('imagesGrid');
            const paginationInfo = document.getElementById('imagesPaginationInfo');
            const pagination = document.getElementById('imagesPagination');

            grid.innerHTML = '<div class="loading">正在加载收藏图片...</div>';
            paginationInfo.style.display = 'none';
            pagination.style.display = 'none';

            try {
                const category = document.getElementById('categoryFilter').value;
                const sort = document.getElementById('sortFilter').value;

                const params = new URLSearchParams({
                    type: 'images',
                    page: currentPage,
                    limit: itemsPerPage,
                    sort: sort
                });
                if (category) {
                    params.append('category', category);
                }

                const response = await fetch(`/api/favorites?${params.toString()}`);
                const data = await response.json();

                console.log(`📊 API response:`, data);

                totalFavorites = data.total;
                // 不在这里设置 currentImages，让 displayFavoriteImages 函数处理
                displayFavoriteImages(data.favorites);
                updateImagesPagination(data.total, data.page, data.total_pages);

            } catch (error) {
                console.error('加载收藏图片失败:', error);
                grid.innerHTML = '<div class="loading">加载失败，请刷新页面重试</div>';
            }
        }

        // 加载收藏图片 (按图集分组模式)
        async function loadFavoriteImagesGrouped(page = 1) {
            currentPage = page;
            const grid = document.getElementById('imagesGrid');
            const paginationInfo = document.getElementById('imagesPaginationInfo');
            const pagination = document.getElementById('imagesPagination');

            grid.innerHTML = '<div class="loading">正在加载分组收藏...</div>';
            paginationInfo.style.display = 'none';
            pagination.style.display = 'none';

            try {
                const category = document.getElementById('categoryFilter').value;
                const sort = document.getElementById('sortFilter').value;

                const params = new URLSearchParams({
                    type: 'images_grouped',
                    page: currentPage,
                    limit: itemsPerPage,
                    sort: sort
                });
                if (category) {
                    params.append('category', category);
                }

                const response = await fetch(`/api/favorites?${params.toString()}`);
                const data = await response.json();

                totalFavorites = data.total;
                displayFavoriteImagesGrouped(data.groups);
                updateImagesPagination(data.total, data.page, data.total_pages);

            } catch (error) {
                console.error('加载分组收藏失败:', error);
                grid.innerHTML = '<div class="loading">加载失败，请刷新页面重试</div>';
            }
        }

        // 加载收藏统计 - 增强版支持图片和图集分别统计
        async function loadFavoritesStats() {
            try {
                const response = await fetch('/api/favorites-stats');
                const stats = await response.json();

                console.log('📊 收藏统计数据:', stats);

                // 总览统计
                document.getElementById('totalFavorites').textContent = stats.total?.favorites || 0;
                document.getElementById('totalCategories').textContent = stats.total?.categories || 0;
                document.getElementById('totalRecent').textContent = stats.total?.recent || 0;

                // 图片收藏统计
                document.getElementById('imagesFavorites').textContent = stats.images?.total || 0;
                document.getElementById('imagesCategories').textContent = stats.images?.categories || 0;
                document.getElementById('imagesAlbums').textContent = stats.images?.albums || 0;
                document.getElementById('imagesRecent').textContent = stats.images?.recent || 0;

                // 图集收藏统计
                document.getElementById('albumsFavorites').textContent = stats.albums?.total || 0;
                document.getElementById('albumsCategories').textContent = stats.albums?.categories || 0;
                document.getElementById('albumsRecent').textContent = stats.albums?.recent || 0;

            } catch (error) {
                console.error('加载统计失败:', error);
                // 设置默认值
                const defaultElements = [
                    'totalFavorites', 'totalCategories', 'totalRecent',
                    'imagesFavorites', 'imagesCategories', 'imagesAlbums', 'imagesRecent',
                    'albumsFavorites', 'albumsCategories', 'albumsRecent'
                ];
                defaultElements.forEach(id => {
                    const element = document.getElementById(id);
                    if (element) element.textContent = '0';
                });
            }
        }

        // 显示收藏图片 (全部显示模式) - 与主页图片展示完全一致
        function displayFavoriteImages(images) {
            console.log(`🖼️ displayFavoriteImages called with ${images ? images.length : 0} images`);

            // 更新全局 currentImages 数组
            currentImages = images || [];
            console.log(`📝 Updated currentImages.length: ${currentImages.length}`);

            // 调试：检查第一张图片的数据结构
            if (currentImages.length > 0) {
                console.log(`🔍 First image data:`, currentImages[0]);
                console.log(`🔍 First image path:`, currentImages[0].path);
            }

            const grid = document.getElementById('imagesGrid');
            const paginationInfo = document.getElementById('imagesPaginationInfo');
            const pagination = document.getElementById('imagesPagination');

            if (!images || images.length === 0) {
                grid.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">💔</div>
                        <h3 class="empty-title">还没有收藏任何图片</h3>
                        <p class="empty-text">去发现一些美好的瞬间，添加到收藏吧！</p>
                        <a href="/" class="btn-primary">🌸 去探索</a>
                    </div>
                `;
                paginationInfo.style.display = 'none';
                pagination.style.display = 'none';
                return;
            }

            // 显示分页信息和控件
            paginationInfo.style.display = 'block';
            pagination.style.display = 'flex';

            // 生成图片网格 - 与主页完全一致的HTML结构
            grid.innerHTML = images.map((image, index) => {
                const imagePath = image.path.startsWith('/images/') ? image.path : `/images/${image.path}`;
                console.log(`🖼️ Image ${index}: path=${image.path}, processed=${imagePath}`);
                return `
                    <div class="image-card" onclick="console.log('🖱️ Image card clicked, index=${index}'); openModal(${index});">
                        <img src="${imagePath}"
                             alt="${image.filename}"
                             loading="lazy"
                             onerror="this.src='${placeholderSvg}'">
                        <div class="image-overlay">
                            <div class="image-actions">
                                <button class="action-btn favorited"
                                        data-image-id="${image.id}"
                                        onclick="event.stopPropagation(); toggleFavorite(${image.id}, this)"
                                        title="取消收藏">
                                    ❤️
                                </button>
                                <button class="action-btn"
                                        onclick="event.stopPropagation(); downloadImage('${imagePath}', '${image.filename}')"
                                        title="下载">
                                    ⬇️
                                </button>
                                <button class="action-btn"
                                        onclick="event.stopPropagation(); console.log('👁️ View button clicked, index=${index}'); openModal(${index})"
                                        title="查看">
                                    👁️
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // 显示收藏图片 (按图集分组模式) - 改为显示图集卡片
        function displayFavoriteImagesGrouped(groups) {
            console.log(`📁 displayFavoriteImagesGrouped called with ${groups ? groups.length : 0} groups`);

            const grid = document.getElementById('imagesGrid');
            const paginationInfo = document.getElementById('imagesPaginationInfo');
            const pagination = document.getElementById('imagesPagination');

            if (!groups || groups.length === 0) {
                grid.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">💔</div>
                        <h3 class="empty-title">还没有收藏任何图片</h3>
                        <p class="empty-text">去发现一些美好的瞬间，添加到收藏吧！</p>
                        <a href="/" class="btn-primary">🌸 去探索</a>
                    </div>
                `;
                paginationInfo.style.display = 'none';
                pagination.style.display = 'none';
                return;
            }

            // 生成图集卡片HTML - 与主页样式一致
            grid.innerHTML = groups.map(group => {
                // 使用收藏图片的第一张作为封面
                const coverImage = group.images[0];
                const coverPath = coverImage.path.startsWith('/images/') ? coverImage.path : `/images/${coverImage.path}`;

                return `
                    <div class="album-card" onclick="openFavoriteAlbum('${group.category}', '${group.album}')">
                        <img src="${coverPath}"
                             alt="${group.album}"
                             class="album-cover"
                             loading="lazy"
                             onerror="this.src='${placeholderSvg}'">
                        <h3 class="album-title">${group.album}</h3>
                        <p class="album-count">${getCategoryDisplayName(group.category)} · ${group.images.length}张收藏</p>
                    </div>
                `;
            }).join('');

            // 隐藏分页控件（分组模式不需要分页）
            paginationInfo.style.display = 'none';
            pagination.style.display = 'none';
        }

        // 打开收藏图集详情页 - 只显示收藏的图片
        function openFavoriteAlbum(category, album) {
            console.log(`📂 Opening favorite album: ${category}/${album}`);

            // 构建URL，添加favorite=true参数表示只显示收藏的图片
            const encodedCategory = encodeURIComponent(category);
            const encodedAlbum = encodeURIComponent(album);
            const url = `/album/${encodedCategory}/${encodedAlbum}?favorite=true`;

            // 跳转到图集页面
            window.location.href = url;
        }

        // 显示收藏 - 统一画廊视图 (与相册页面完全一致)
        function displayFavorites(favorites) {
            const grid = document.getElementById('favoritesGrid');
            const galleryControls = document.getElementById('galleryControls');

            if (!favorites || favorites.length === 0) {
                grid.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">💔</div>
                        <h3 class="empty-title">还没有收藏任何内容</h3>
                        <p class="empty-text">去发现一些美好的瞬间，添加到收藏吧！</p>
                        <a href="/" class="btn-primary">🌸 去探索</a>
                    </div>
                `;
                galleryControls.style.display = 'none';
                return;
            }

            // 显示视图控制
            galleryControls.style.display = 'flex';

            // 为全部显示模式创建虚拟图集数据
            currentImages = favorites.map((favorite, index) => ({
                id: favorite.id,
                path: favorite.path,
                filename: favorite.filename,
                category: favorite.category,
                album: favorite.album
            }));

            // 使用与相册相同的画廊布局
            grid.className = 'gallery-grid';
            grid.innerHTML = favorites.map((favorite, index) => `
                <div class="image-card" onclick="openModal(${index})" data-image-id="${favorite.id}">
                    <img src="${favorite.path}" alt="${favorite.filename}" loading="lazy" onerror="this.src='${placeholderSvg}'">
                    <div class="image-overlay">
                        <div class="image-actions">
                            <button class="action-btn favorited"
                                    onclick="event.stopPropagation(); removeFavorite('${favorite.id}', event)"
                                    title="移除收藏">
                                💔
                            </button>
                            <button class="action-btn" onclick="event.stopPropagation(); downloadImage('${favorite.path}', '${favorite.filename}')" title="下载">
                                ⬇️
                            </button>
                            <button class="action-btn" onclick="event.stopPropagation(); openModal(${index})" title="查看">
                                👁️
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 获取分类中文名
        function getCategoryName(category) {
            const names = {
                'korea': '韩系',
                'cosplay': '角色扮演',
                'japan': '日系',
                'gravure': '写真',
                'chinese': '中式',
                'thailand': '泰式'
            };
            return names[category] || category;
        }

        // 设置视图大小 - 与相册页面一致
        function setViewSize(size) {
            const grid = document.getElementById('favoritesGrid');
            const buttons = document.querySelectorAll('.view-btn');

            // 更新按钮状态
            buttons.forEach(btn => btn.classList.remove('active'));
            document.querySelector(`[data-size="${size}"]`).classList.add('active');

            // 更新网格类
            grid.className = `gallery-grid ${size === 'medium' ? '' : size}`;
        }



        // 关闭模态框
        function closeModal() {
            const modal = document.getElementById('imageModal');
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        // 上一张图片
        function prevImage() {
            if (currentModalIndex > 0) {
                openModal(currentModalIndex - 1);
            }
        }

        // 下一张图片
        function nextImage() {
            if (currentModalIndex < currentImages.length - 1) {
                openModal(currentModalIndex + 1);
            }
        }

        // 模态框相关函数 - 基于主页的完整实现
        function openModal(index) {
            console.log(`🔍 openModal called with index: ${index}`);
            console.log(`currentImages.length: ${currentImages ? currentImages.length : 'undefined'}`);

            if (!currentImages || currentImages.length === 0) {
                console.error('❌ currentImages is empty or undefined');
                return;
            }
            if (index < 0 || index >= currentImages.length) {
                console.error(`❌ Invalid index: ${index}, array length: ${currentImages.length}`);
                return;
            }

            currentModalIndex = index;
            const modal = document.getElementById('imageModal');

            // 使用 updateModal 函数来设置内容
            updateModal();

            modal.style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        function closeModal() {
            const modal = document.getElementById('imageModal');
            if (modal) {
                modal.style.display = 'none';
                document.body.style.overflow = 'auto';
            }
        }

        function prevImage() {
            if (currentModalIndex > 0) {
                openModal(currentModalIndex - 1);
            }
        }

        function nextImage() {
            if (currentModalIndex < currentImages.length - 1) {
                openModal(currentModalIndex + 1);
            }
        }



        // 更新模态框收藏按钮状态 - 收藏页面版本
        function updateModalFavoriteButton() {
            // 收藏页面没有收藏按钮，所以这个函数什么都不做
            // 保留这个函数是为了与其他页面的接口保持一致
            return;
        }

        // 下载图片
        function downloadImage(imagePath, filename) {
            const link = document.createElement('a');
            link.href = imagePath;
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // 切换收藏状态
        async function toggleFavorite(imageId, buttonElement) {
            try {
                const response = await fetch('/api/favorite', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        image_id: imageId
                    })
                });

                const result = await response.json();
                if (result.success) {
                    // 更新按钮状态
                    if (result.favorited) {
                        buttonElement.classList.add('favorited');
                        if (typeof userFavorites !== 'undefined') {
                            userFavorites.add(imageId.toString());
                        }
                        showToast('已收藏');
                    } else {
                        buttonElement.classList.remove('favorited');
                        if (typeof userFavorites !== 'undefined') {
                            userFavorites.delete(imageId.toString());
                        }
                        showToast('已取消收藏');

                        // 如果在收藏页面，重新加载当前页面
                        if (currentFavoritesTab === 'images') {
                            if (currentImagesMode === 'all') {
                                loadFavoriteImages(currentPage);
                            } else {
                                loadFavoriteImagesGrouped(currentPage);
                            }
                        }
                    }
                } else {
                    showToast('操作失败，请重试', 'error');
                }
            } catch (error) {
                console.error('收藏操作失败:', error);
                showToast('操作失败，请重试', 'error');
            }
        }

        // 显示提示消息
        function showToast(message, type = 'success') {
            // 创建提示元素
            const toast = document.createElement('div');

            let backgroundColor;
            switch(type) {
                case 'error':
                    backgroundColor = 'rgba(220, 53, 69, 0.9)';
                    break;
                case 'info':
                    backgroundColor = 'rgba(13, 202, 240, 0.9)';
                    break;
                case 'warning':
                    backgroundColor = 'rgba(255, 193, 7, 0.9)';
                    break;
                default: // success
                    backgroundColor = 'rgba(25, 135, 84, 0.9)';
            }

            toast.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: ${backgroundColor};
                color: white;
                padding: 1rem 2rem;
                border-radius: 25px;
                z-index: 3000;
                font-size: 1rem;
                pointer-events: none;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
                backdrop-filter: blur(10px);
            `;
            toast.textContent = message;
            document.body.appendChild(toast);

            // 3秒后移除
            setTimeout(() => {
                if (document.body.contains(toast)) {
                    document.body.removeChild(toast);
                }
            }, 3000);
        }

        // 视图大小控制
        function setViewSize(size) {
            const grid = document.getElementById('imagesGrid');
            const buttons = document.querySelectorAll('.view-btn');

            // 更新按钮状态
            buttons.forEach(btn => btn.classList.remove('active'));
            document.querySelector(`[data-size="${size}"]`).classList.add('active');

            // 更新网格样式
            grid.className = `gallery-grid ${size}`;
        }

        // 键盘导航 - 与主页一致
        document.addEventListener('keydown', function(event) {
            const modal = document.getElementById('imageModal');
            if (modal && modal.style.display === 'block') {
                switch(event.key) {
                    case 'ArrowLeft':
                        event.preventDefault();
                        prevImage();
                        break;
                    case 'ArrowRight':
                        event.preventDefault();
                        nextImage();
                        break;
                    case 'Escape':
                        event.preventDefault();
                        closeModal();
                        break;
                }
            }
        });

        // 手机端触摸滑动支持
        let touchStartX = 0;
        let touchEndX = 0;

        document.getElementById('imageModal').addEventListener('touchstart', function(e) {
            touchStartX = e.changedTouches[0].screenX;
        }, false);

        document.getElementById('imageModal').addEventListener('touchend', function(e) {
            touchEndX = e.changedTouches[0].screenX;
            handleSwipe();
        }, false);

        function handleSwipe() {
            const swipeThreshold = 50; // 最小滑动距离
            const swipeDistance = touchEndX - touchStartX;

            if (Math.abs(swipeDistance) > swipeThreshold) {
                if (swipeDistance > 0) {
                    // 向右滑动 - 上一张图片
                    prevImage();
                } else {
                    // 向左滑动 - 下一张图片
                    nextImage();
                }
            }
        }

        // 点击模态框背景关闭
        document.getElementById('imageModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        // 更新分页 (现在基于总数计算)
        function updatePagination() {
            const totalPages = Math.ceil(totalFavorites / itemsPerPage);
            const pagination = document.getElementById('pagination');

            if (totalPages <= 1) {
                pagination.innerHTML = '';
                return;
            }

            const paginationHTML = generatePagination(currentPage, totalPages, 'changePage');
            pagination.innerHTML = paginationHTML;
        }

        // 切换页面 (现在会触发 API 请求)
        function changePage(page) {
            if (page < 1 || page > Math.ceil(totalFavorites / itemsPerPage)) {
                return;
            }

            // 检查当前是否在查看特定图集的收藏图片
            if (window.currentFavoritedAlbum) {
                loadFavoritedAlbumOnly(window.currentFavoritedAlbum.category, window.currentFavoritedAlbum.album, page);
            } else if (currentFavoritesTab === 'albums') {
                loadFavoritedAlbums(page);
            } else if (currentImagesMode === 'all') {
                loadFavorites(page);
            } else {
                loadFavoritesGrouped(page);
            }

            window.scrollTo({ top: 0, behavior: 'smooth' });
        }

        // 加载收藏的图集
        async function loadFavoritedAlbums(page = 1) {
            currentPage = page;
            const grid = document.getElementById('favoritesGrid');
            grid.innerHTML = '<div class="loading">正在加载收藏图集...</div>';

            try {
                const category = document.getElementById('albumCategoryFilter').value;
                const sort = document.getElementById('albumSortFilter').value;

                const params = new URLSearchParams({
                    page: currentPage,
                    limit: 16 // 与主页一致
                });

                if (category) params.append('category', category);
                if (sort) params.append('sort', sort);

                const response = await fetch(`/api/favorited-albums?${params.toString()}`);
                const data = await response.json();

                totalFavorites = data.total;
                displayFavoritedAlbums(data.albums);
                updateFavoriteAlbumsPagination(data.total, Math.ceil(data.total / 16));

            } catch (error) {
                console.error('加载收藏图集失败:', error);
                grid.innerHTML = '<div class="loading">加载失败，请刷新页面重试</div>';
            }
        }

        // 更新图片分页控件 - 使用主页的完整分页系统
        function updateImagesPagination(total, page, totalPages) {
            const paginationInfo = document.getElementById('imagesPaginationInfo');
            const paginationContainer = document.getElementById('imagesPagination');
            const currentInfo = document.getElementById('imagesCurrentInfo');

            // 更新信息显示
            const startItem = (page - 1) * itemsPerPage + 1;
            const endItem = Math.min(page * itemsPerPage, total);
            currentInfo.textContent = `第 ${page} 页，共 ${total} 张图片 (${startItem}-${endItem})`;

            // 显示分页信息
            paginationInfo.style.display = 'block';

            // 使用主页的分页生成函数
            if (totalPages > 1) {
                paginationContainer.innerHTML = generatePagination(page, totalPages, 'loadImagesPage');
                paginationContainer.style.display = 'block';
            } else {
                paginationContainer.style.display = 'none';
            }
        }

        // 加载图片页面
        function loadImagesPage(page) {
            if (page < 1) return;

            if (currentImagesMode === 'all') {
                loadFavoriteImages(page);
            } else {
                loadFavoriteImagesGrouped(page);
            }
        }

        // 获取分类显示名称
        function getCategoryDisplayName(category) {
            const categoryNames = {
                'korea': '韩系',
                'cosplay': '角色扮演',
                'japan': '日系',
                'gravure': '写真',
                'chinese': '中式',
                'thailand': '泰式'
            };
            return categoryNames[category] || category;
        }

        // 查看图集
        function viewAlbum(category, album) {
            window.open(`/album/${category}/${encodeURIComponent(album)}`, '_blank');
        }

        // 取消收藏图集中的所有图片
        async function unfavoriteAlbumImages(category, album) {
            if (!confirm(`确定要取消收藏图集"${album}"中的所有图片吗？`)) {
                return;
            }

            try {
                const response = await fetch('/api/unfavorite-album-all', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        category: category,
                        album: album
                    })
                });

                const result = await response.json();
                if (result.success) {
                    showToast(`成功取消收藏 ${result.count} 张图片`);
                    // 重新加载当前页面
                    if (currentImagesMode === 'all') {
                        loadFavoriteImages(currentPage);
                    } else {
                        loadFavoriteImagesGrouped(currentPage);
                    }
                } else {
                    showToast(result.message || '取消收藏失败', 'error');
                }
            } catch (error) {
                console.error('取消收藏失败:', error);
                showToast('取消收藏失败，请重试', 'error');
            }
        }



        // 过滤收藏图集
        function filterFavoritedAlbums() {
            currentPage = 1; // 重置页码
            loadFavoritedAlbums(1);
        }

        // 显示收藏的图集
        function displayFavoritedAlbums(albums) {
            const grid = document.getElementById('favoritesGrid');
            const galleryControls = document.getElementById('galleryControls');

            if (!albums || albums.length === 0) {
                grid.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">💔</div>
                        <h3 class="empty-title">还没有收藏任何图集</h3>
                        <p class="empty-text">去发现一些美好的图集，添加到收藏吧！</p>
                        <a href="/" class="btn-primary">🌸 去探索</a>
                    </div>
                `;
                galleryControls.style.display = 'none';
                return;
            }

            // 隐藏图片视图控制
            galleryControls.style.display = 'none';

            // 使用完全和主页一样的简洁风格布局，添加垃圾桶按钮
            grid.className = 'albums-grid';
            grid.innerHTML = albums.map(album => `
                <div class="album-card" onclick="openAlbumDetail('${album.category}', '${album.album}')">
                    <div class="album-unfavorite-btn" onclick="event.stopPropagation(); unfavoriteAlbum('${album.category}', '${album.album}')" title="取消收藏">
                        🗑️
                    </div>
                    <img src="${album.cover_image}" alt="${album.album}" class="album-cover" loading="lazy" onerror="this.src='${placeholderSvg}'">
                    <h3 class="album-title">${album.album}</h3>
                    <p class="album-count">${getCategoryName(album.category)} · ${album.image_count}张图片</p>
                </div>
            `).join('');
        }

        // 加载按图集分组的收藏图片
        async function loadFavoritesGrouped(page = 1) {
            currentPage = page;
            const grid = document.getElementById('favoritesGrid');
            grid.innerHTML = '<div class="loading">正在加载分组收藏...</div>';

            try {
                const params = new URLSearchParams({
                    page: currentPage,
                    limit: itemsPerPage
                });

                const response = await fetch(`/api/favorites-grouped?${params.toString()}`);
                const data = await response.json();

                totalFavorites = data.total;
                displayFavoritesGrouped(data.albums);
                updatePagination();

            } catch (error) {
                console.error('加载分组收藏失败:', error);
                grid.innerHTML = '<div class="loading">加载失败，请刷新页面重试</div>';
            }
        }

        // 显示按图集分组的收藏图片
        function displayFavoritesGrouped(albums) {
            const grid = document.getElementById('favoritesGrid');

            if (!albums || albums.length === 0) {
                grid.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">💔</div>
                        <h3 class="empty-title">还没有收藏任何图片</h3>
                        <p class="empty-text">去发现一些美好的瞬间，添加到收藏吧！</p>
                        <a href="/" class="btn-primary">🌸 去探索</a>
                    </div>
                `;
                return;
            }

            // 使用相册卡片布局
            grid.className = 'albums-grid';
            grid.innerHTML = albums.map(album => `
                <div class="album-card" onclick="viewFavoritedAlbumImages('${album.category}', '${album.album}')">
                    <img src="${album.cover_image}" alt="${album.album}" loading="lazy" onerror="this.src='${placeholderSvg}'">
                    <div class="album-info">
                        <h3 class="album-title">${album.album}</h3>
                        <div class="album-meta">
                            <span class="album-category">${getCategoryName(album.category)}</span>
                            <span class="album-count">收藏 ${album.favorite_count} 张</span>
                        </div>
                        <div class="album-date">
                            最近收藏于 ${new Date(album.latest_favorited).toLocaleDateString()}
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 过滤收藏 (重置到第一页并重新加载) - 修复函数名
        function filterFavorites() {
            console.log('🔄 filterFavorites called');
            console.log(`currentFavoritesTab: ${currentFavoritesTab}`);
            console.log(`currentImagesMode: ${currentImagesMode}`);

            currentPage = 1;
            if (currentFavoritesTab === 'albums') {
                loadFavoritedAlbums(1);
            } else if (currentImagesMode === 'all') {
                loadFavoriteImages(1);
            } else {
                loadFavoriteImagesGrouped(1);
            }
        }

        // 查看图集
        function viewAlbum(category, album) {
            window.location.href = `/album/${category}/${encodeURIComponent(album)}`;
        }

        // 移除收藏
        async function removeFavorite(imageId, event) {
            event.stopPropagation();
            if (!confirm('确定要移除这个收藏吗？')) {
                return;
            }
            
            try {
                const response = await fetch('/api/favorite', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ image_id: imageId })
                });
                
                const result = await response.json();
                if (result.success) {
                    // 重新加载当前页的收藏列表和统计数据
                    loadFavorites(currentPage); 
                    loadFavoritesStats();
                } else {
                    alert(result.error || "移除失败");
                }
            } catch (error) {
                console.error('移除收藏失败:', error);
                alert("操作失败，请检查网络");
            }
        }

        // 取消收藏图集
        async function unfavoriteAlbum(category, album) {
            if (!confirm(`确定要取消收藏图集"${album}"吗？`)) {
                return;
            }

            try {
                const response = await fetch('/api/album-favorite', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        category: category,
                        album: album
                    })
                });

                const data = await response.json();
                if (data.success) {
                    // 显示成功消息（可以用alert或者自定义消息组件）
                    console.log(data.message);

                    // 智能页面处理：如果当前页面没有数据了，回到上一页
                    const grid = document.getElementById('favoritesGrid');
                    const currentAlbumCards = grid.querySelectorAll('.album-card');

                    // 如果当前页只有一个图集且不是第一页，回到上一页
                    if (currentAlbumCards.length === 1 && currentPage > 1) {
                        loadFavoritedAlbums(currentPage - 1);
                    } else {
                        // 否则刷新当前页
                        loadFavoritedAlbums(currentPage);
                    }

                    // 更新统计信息
                    loadFavoritesStats();
                } else {
                    alert(data.message || '操作失败');
                }
            } catch (error) {
                console.error('取消收藏图集失败:', error);
                alert('网络错误，请重试');
            }
        }

        // 查看收藏图集中的图片 - 仅显示收藏的图片
        function viewFavoritedAlbumImages(category, album) {
            // 切换到全部显示模式并加载该图集的收藏图片
            switchImagesMode('all');
            loadFavoritedAlbumOnly(category, album);
        }

        // 加载指定图集中的收藏图片
        async function loadFavoritedAlbumOnly(category, album, page = 1) {
            currentPage = page;
            // 设置当前查看的图集上下文
            window.currentFavoritedAlbum = { category, album };

            const grid = document.getElementById('favoritesGrid');
            grid.innerHTML = '<div class="loading">正在加载收藏图片...</div>';

            try {
                const params = new URLSearchParams({
                    page: currentPage,
                    limit: itemsPerPage
                });

                const response = await fetch(`/api/album-favorites-only/${category}/${album}?${params.toString()}`);
                const data = await response.json();

                totalFavorites = data.total;

                // 显示图集标题
                const categoryName = getCategoryName(category);
                grid.innerHTML = `
                    <div class="album-header" style="grid-column: 1 / -1; text-align: center; margin-bottom: 2rem;">
                        <h2 style="color: #8b4513; margin-bottom: 0.5rem;">📂 ${album}</h2>
                        <p style="color: #cd853f;">收藏的图片 (${data.total} 张)</p>
                        <button onclick="switchImagesMode('grouped')" style="margin-top: 1rem; padding: 0.5rem 1rem; background: linear-gradient(45deg, #ff69b4, #ffa500); color: white; border: none; border-radius: 20px; cursor: pointer;">
                            ← 返回分组视图
                        </button>
                    </div>
                `;

                if (data.images && data.images.length > 0) {
                    // 为收藏图片创建虚拟图集数据
                    currentImages = data.images.map((image, index) => ({
                        id: image.id,
                        path: image.path,
                        filename: image.filename,
                        category: category,
                        album: album
                    }));

                    // 使用画廊布局显示图片
                    grid.className = 'gallery-grid';
                    grid.innerHTML += data.images.map((image, index) => {
                        // 修复图片路径：确保路径以 /images/ 开头
                        const imagePath = image.path.startsWith('/images/') ? image.path : `/images/${image.path}`;
                        return `
                        <div class="image-card" data-image-id="${image.id}">
                            <img src="${imagePath}" alt="${image.filename}" loading="lazy" onerror="this.src='${placeholderSvg}'">
                            <div class="image-overlay">
                                <div class="image-actions">
                                    <button class="action-btn favorited"
                                            onclick="event.stopPropagation(); removeFavorite('${image.id}', event)"
                                            title="移除收藏">
                                        💔
                                    </button>
                                    <button class="action-btn" onclick="event.stopPropagation(); downloadImage('${imagePath}', '${image.filename}')" title="下载">
                                        ⬇️
                                    </button>
                                    <button class="action-btn" onclick="event.stopPropagation(); openModal(${index})" title="查看">
                                        👁️
                                    </button>
                                </div>
                            </div>
                        </div>
                        `;
                    }).join('');
                } else {
                    grid.innerHTML += `
                        <div class="empty-state" style="grid-column: 1 / -1;">
                            <div class="empty-icon">💔</div>
                            <h3 class="empty-title">该图集中没有收藏的图片</h3>
                            <p class="empty-text">去该图集中添加一些收藏吧！</p>
                        </div>
                    `;
                }

                updatePagination();

            } catch (error) {
                console.error('加载收藏图片失败:', error);
                grid.innerHTML = '<div class="loading">加载失败，请刷新页面重试</div>';
            }
        }

        // 清空所有图片收藏
        async function clearAllFavorites() {
            if (!confirm('确定要清空所有图片收藏吗？此操作不可恢复！')) {
                return;
            }

            try {
                const response = await fetch('/api/favorites/clear', { method: 'POST' });
                const result = await response.json();
                if (result.success) {
                    // 重新加载
                    if (currentImagesMode === 'all') {
                        loadFavorites(1);
                    } else {
                        loadFavoritesGrouped(1);
                    }
                    loadFavoritesStats();
                    alert('已清空所有图片收藏');
                }
            } catch (error) {
                console.error('清空收藏失败:', error);
            }
        }

        // 清空所有图集收藏
        async function clearAllAlbumFavorites() {
            if (!confirm('确定要清空所有图集收藏吗？此操作不可恢复！')) {
                return;
            }

            try {
                const response = await fetch('/api/clear-album-favorites', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const result = await response.json();
                if (result.success) {
                    loadFavoritedAlbums(1);
                    loadFavoritesStats();
                    alert('已清空所有图集收藏');
                } else {
                    alert('清空失败：' + result.message);
                }
            } catch (error) {
                console.error('清空图集收藏失败:', error);
                alert('网络错误，请重试');
            }
        }

        // 模态框功能
        function openModal(index) {
            if (index < 0 || index >= currentImages.length) {
                console.error('Invalid image index:', index);
                return;
            }
            currentModalIndex = index;
            updateModal();
            const modal = document.getElementById('imageModal');
            if (modal) {
                modal.style.display = 'block';
                document.body.style.overflow = 'hidden';
            }
        }

        function closeModal() {
            const modal = document.getElementById('imageModal');
            if (modal) {
                modal.style.display = 'none';
                document.body.style.overflow = 'auto';
            }
        }

        function updateModal() {
            console.log(`🔄 updateModal called, currentModalIndex: ${currentModalIndex}`);
            if (currentImages.length === 0) {
                console.error('❌ currentImages is empty in updateModal');
                return;
            }

            const image = currentImages[currentModalIndex];
            const modalImage = document.getElementById('modalImage');
            const modalInfo = document.getElementById('modalInfo'); // 修复：使用正确的ID

            console.log(`🖼️ Updating modal with image:`, image);

            if (modalImage && modalInfo) {
                // 修复图片路径：确保路径以 /images/ 开头
                const imagePath = image.path.startsWith('/images/') ? image.path : `/images/${image.path}`;
                console.log(`📁 Setting modal image src to: ${imagePath}`);
                modalImage.src = imagePath;
                modalImage.alt = image.filename;
                modalInfo.textContent = `${currentModalIndex + 1} / ${currentImages.length}`;
            } else {
                console.error('❌ Modal elements not found:', {modalImage: !!modalImage, modalInfo: !!modalInfo});
            }

            // 更新模态框收藏按钮状态
            updateModalFavoriteButton();
        }





        function prevImage() {
            if (currentImages.length === 0) return;
            currentModalIndex = (currentModalIndex - 1 + currentImages.length) % currentImages.length;
            updateModal();
        }

        function nextImage() {
            if (currentImages.length === 0) return;
            currentModalIndex = (currentModalIndex + 1) % currentImages.length;
            updateModal();
        }



        // 打开图集详情页面
        function openAlbumDetail(category, album) {
            // 跳转到图集详情页面，与主页的跳转逻辑一致
            window.location.href = `/album/${encodeURIComponent(category)}/${encodeURIComponent(album)}`;
        }

        // 显示取消收藏菜单
        function showUnfavoriteMenu(event, category, album) {
            if (confirm(`确定要取消收藏图集 "${album}" 吗？`)) {
                unfavoriteAlbum(category, album);
            }
        }

        // 通用分页生成函数 - 与主页完全一致
        function generatePagination(currentPage, totalPages, onPageClick) {
            if (totalPages <= 1) return '';

            let paginationHTML = '<div class="pagination-wrapper"><div class="pagination-container">';

            // 上一页按钮
            const prevDisabled = currentPage <= 1;
            paginationHTML += `<button class="page-nav-btn" onclick="${onPageClick}(${currentPage - 1})" ${prevDisabled ? 'disabled' : ''}>上一页</button>`;

            // 页码逻辑
            const delta = 2; // 当前页前后显示的页数
            const range = [];
            const rangeWithDots = [];

            // 计算显示的页码范围
            for (let i = Math.max(2, currentPage - delta); i <= Math.min(totalPages - 1, currentPage + delta); i++) {
                range.push(i);
            }

            // 添加第一页
            if (currentPage - delta > 2) {
                rangeWithDots.push(1, '...');
            } else {
                rangeWithDots.push(1);
            }

            // 添加中间页码
            rangeWithDots.push(...range);

            // 添加最后一页
            if (currentPage + delta < totalPages - 1) {
                rangeWithDots.push('...', totalPages);
            } else {
                rangeWithDots.push(totalPages);
            }

            // 生成页码按钮
            rangeWithDots.forEach(page => {
                if (page === '...') {
                    paginationHTML += '<span class="page-ellipsis">...</span>';
                } else {
                    const activeClass = page === currentPage ? 'active' : '';
                    paginationHTML += `<button class="page-btn ${activeClass}" onclick="${onPageClick}(${page})">${page}</button>`;
                }
            });

            // 下一页按钮
            const nextDisabled = currentPage >= totalPages;
            paginationHTML += `<button class="page-nav-btn" onclick="${onPageClick}(${currentPage + 1})" ${nextDisabled ? 'disabled' : ''}>下一页</button>`;

            // 跳转控件
            paginationHTML += `
                <div class="page-jump-container">
                    <span class="page-jump-label">跳转到:</span>
                    <select class="page-jump-select" id="pageJumpSelect_${onPageClick}" onchange="jumpToPageFromSelect('${onPageClick}', this.value, ${totalPages})">
                        <option value="">选择页面</option>`;

            // 生成页码选项
            for (let i = 1; i <= Math.min(totalPages, 100); i += 10) {
                const endPage = Math.min(i + 9, totalPages);
                paginationHTML += `<optgroup label="第 ${i}-${endPage} 页">`;
                for (let j = i; j <= endPage; j++) {
                    const selected = j === currentPage ? 'selected' : '';
                    paginationHTML += `<option value="${j}" ${selected}>第 ${j} 页</option>`;
                }
                paginationHTML += '</optgroup>';
            }

            paginationHTML += `
                    </select>
                    <input type="number" class="page-jump-input" id="pageJumpInput_${onPageClick}" placeholder="页码" min="1" max="${totalPages}" onkeypress="handlePageJumpKeypress(event, '${onPageClick}', ${totalPages})">
                    <button class="page-jump-btn" onclick="jumpToPageFromInput('${onPageClick}', ${totalPages})">跳转</button>
                </div>`;

            paginationHTML += '</div></div>';
            return paginationHTML;
        }

        // 页面跳转功能 - 与主页完全一致
        function jumpToPageFromSelect(onPageClick, page, totalPages) {
            const pageNum = parseInt(page);
            if (pageNum && pageNum >= 1 && pageNum <= totalPages) {
                window[onPageClick](pageNum);
            }
        }

        function jumpToPageFromInput(onPageClick, totalPages) {
            const input = document.getElementById(`pageJumpInput_${onPageClick}`);
            const pageNum = parseInt(input.value);

            if (!pageNum || pageNum < 1 || pageNum > totalPages) {
                input.style.borderColor = '#ff6b6b';
                setTimeout(() => {
                    input.style.borderColor = '#ffb6c1';
                }, 1000);
                return;
            }

            input.value = '';
            window[onPageClick](pageNum);
        }

        function handlePageJumpKeypress(event, onPageClick, totalPages) {
            if (event.key === 'Enter') {
                jumpToPageFromInput(onPageClick, totalPages);
            }
        }



        // 更新收藏图集分页
        function updateFavoriteAlbumsPagination(total, totalPages) {
            const albumsContainer = document.getElementById('albumsContainer');

            // 移除现有分页
            const existingPagination = document.querySelector('.pagination-wrapper');
            if (existingPagination) {
                existingPagination.remove();
            }

            // 添加新分页
            if (totalPages > 1) {
                const paginationHTML = generatePagination(currentPage, totalPages, 'loadFavoritedAlbums');
                albumsContainer.insertAdjacentHTML('afterend', paginationHTML);
            }
        }



        // 下载图片功能
        function downloadImage(imagePath, filename) {
            const link = document.createElement('a');
            link.href = imagePath;
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // User dropdown functionality - 重新设计
        function toggleUserDropdown() {
            if (window.innerWidth <= 768) {
                // 移动端使用模态框
                const mobileModal = document.getElementById('mobileDropdownModal');
                if (mobileModal) {
                    mobileModal.classList.toggle('show');
                }
            } else {
                // 桌面端使用下拉菜单
                const dropdown = document.getElementById('userDropdown');
                if (dropdown) {
                    dropdown.classList.toggle('show');
                }
            }
        }

        // 关闭下拉菜单
        function closeDropdowns() {
            const dropdown = document.getElementById('userDropdown');
            const mobileModal = document.getElementById('mobileDropdownModal');

            if (dropdown) dropdown.classList.remove('show');
            if (mobileModal) mobileModal.classList.remove('show');
        }

        // 点击外部关闭
        document.addEventListener('click', function(event) {
            const userInfo = document.querySelector('.user-dropdown');
            const mobileModal = document.getElementById('mobileDropdownModal');

            // 如果点击的不是用户信息区域
            if (userInfo && !userInfo.contains(event.target)) {
                closeDropdowns();
            }

            // 如果点击的是移动端模态框背景
            if (event.target === mobileModal) {
                closeDropdowns();
            }
        });

        // 触摸事件支持
        document.addEventListener('touchstart', function(event) {
            const userInfo = document.querySelector('.user-dropdown');
            const mobileModal = document.getElementById('mobileDropdownModal');

            if (userInfo && !userInfo.contains(event.target) && event.target !== mobileModal) {
                closeDropdowns();
            }
        });
    </script>

    <!-- 移动端用户下拉菜单模态框 -->
    <div class="mobile-dropdown-modal" id="mobileDropdownModal">
        <div class="mobile-dropdown-content">
            <a href="/change-password" class="mobile-dropdown-item">修改密码</a>
            <a href="/logout" class="mobile-dropdown-item">退出</a>
        </div>
    </div>
</body>
</html>'''

# 登录页面模板
LOGIN_TEMPLATE = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 春色写真馆</title>
    <link rel="icon" type="image/svg+xml" href="/favicon.ico">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* 移除所有列表标记 */
        ul, ol { list-style: none; }
        li::marker { display: none; }

        body {
            font-family: 'PingFang SC', 'Hiragino Sans GB', 'Noto Sans CJK SC', 'Source Han Sans SC', 'Microsoft YaHei', '微软雅黑', sans-serif;
            background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 25%, #f0fff4 50%, #fff8dc 75%, #fdf5e6 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #5d4e37;
        }
        
        .login-container {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            padding: 3rem;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(255, 182, 193, 0.3);
            width: 100%;
            max-width: 400px;
            position: relative;
            overflow: hidden;
        }
        
        .login-container::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 182, 193, 0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        
        .login-title {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 2rem;
            background: linear-gradient(45deg, #ff69b4, #ffa500, #98fb98);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            position: relative;
            z-index: 1;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
            position: relative;
            z-index: 1;
        }
        
        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            color: #8b4513;
            font-weight: bold;
        }
        
        .form-input {
            width: 100%;
            padding: 1rem;
            border: 2px solid #ffb6c1;
            border-radius: 10px;
            font-size: 1rem;
            background: rgba(255, 255, 255, 0.8);
            transition: all 0.3s ease;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #ff69b4;
            box-shadow: 0 0 15px rgba(255, 105, 180, 0.3);
        }
        
        .btn-login {
            width: 100%;
            padding: 1rem;
            background: linear-gradient(45deg, #ff69b4, #ffa500);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            z-index: 1;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 105, 180, 0.4);
        }
        
        .login-links {
            text-align: center;
            margin-top: 2rem;
            position: relative;
            z-index: 1;
        }
        
        .login-link {
            color: #8b4513;
            text-decoration: none;
            margin: 0 1rem;
            transition: color 0.3s ease;
        }
        
        .login-link:hover {
            color: #ff69b4;
        }
        
        .error-message {
            background: rgba(255, 0, 0, 0.1);
            color: #d32f2f;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .success-message {
            background: rgba(0, 255, 0, 0.1);
            color: #2e7d32;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1rem;
            text-align: center;
        }

        /* Mobile responsive styles */
        @media (max-width: 768px) {
            .login-container {
                padding: 20px 15px;
                margin: 10px;
                min-height: calc(100vh - 20px);
            }

            .login-title {
                font-size: 2rem;
                margin-bottom: 1.5rem;
            }

            .login-form {
                padding: 20px;
            }

            .form-group {
                margin-bottom: 1rem;
            }

            .form-input {
                padding: 12px 15px;
                font-size: 1rem;
            }

            .btn-login {
                padding: 12px;
                font-size: 1rem;
            }

            .form-links {
                flex-direction: column;
                gap: 10px;
                text-align: center;
            }

            .form-links a {
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <h1 class="login-title"><a href="/" style="text-decoration: none; color: inherit;">🌸 登录</a></h1>
        
        {% if error %}
        <div class="error-message">{{ error }}</div>
        {% endif %}
        
        {% if success %}
        <div class="success-message">{{ success }}</div>
        {% endif %}
        
        <form method="POST">
            <div class="form-group">
                <label class="form-label" for="username">用户名</label>
                <input type="text" class="form-input" id="username" name="username" required>
            </div>
            
            <div class="form-group">
                <label class="form-label" for="password">密码</label>
                <input type="password" class="form-input" id="password" name="password" required>
            </div>
            
            <button type="submit" class="btn-login">登录</button>
        </form>
        
        <div class="login-links">
            <a href="/register" class="login-link">注册账号</a>
            <a href="/change-password" class="login-link">忘记密码</a>
            <a href="/" class="login-link">返回首页</a>
        </div>
    </div>
</body>
</html>'''

# 注册页面模板
REGISTER_TEMPLATE = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注册 - 春色写真馆</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* 移除所有列表标记 */
        ul, ol { list-style: none; }
        li::marker { display: none; }

        body {
            font-family: 'PingFang SC', 'Hiragino Sans GB', 'Noto Sans CJK SC', 'Source Han Sans SC', 'Microsoft YaHei', '微软雅黑', sans-serif;
            background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 25%, #f0fff4 50%, #fff8dc 75%, #fdf5e6 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #5d4e37;
        }
        
        .register-container {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            padding: 3rem;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(255, 182, 193, 0.3);
            width: 100%;
            max-width: 400px;
            position: relative;
            overflow: hidden;
        }
        
        .register-container::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 182, 193, 0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        
        .register-title {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 2rem;
            background: linear-gradient(45deg, #ff69b4, #ffa500, #98fb98);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            position: relative;
            z-index: 1;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
            position: relative;
            z-index: 1;
        }
        
        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            color: #8b4513;
            font-weight: bold;
        }
        
        .form-input {
            width: 100%;
            padding: 1rem;
            border: 2px solid #ffb6c1;
            border-radius: 10px;
            font-size: 1rem;
            background: rgba(255, 255, 255, 0.8);
            transition: all 0.3s ease;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #ff69b4;
            box-shadow: 0 0 15px rgba(255, 105, 180, 0.3);
        }
        
        .btn-register {
            width: 100%;
            padding: 1rem;
            background: linear-gradient(45deg, #ff69b4, #ffa500);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            z-index: 1;
        }
        
        .btn-register:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 105, 180, 0.4);
        }
        
        .register-links {
            text-align: center;
            margin-top: 2rem;
            position: relative;
            z-index: 1;
        }
        
        .register-link {
            color: #8b4513;
            text-decoration: none;
            margin: 0 1rem;
            transition: color 0.3s ease;
        }
        
        .register-link:hover {
            color: #ff69b4;
        }
        
        .error-message {
            background: rgba(255, 0, 0, 0.1);
            color: #d32f2f;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .success-message {
            background: rgba(0, 255, 0, 0.1);
            color: #2e7d32;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1rem;
            text-align: center;
        }

        /* Mobile responsive styles */
        @media (max-width: 768px) {
            .register-container {
                padding: 20px 15px;
                margin: 10px;
                min-height: calc(100vh - 20px);
            }

            .register-title {
                font-size: 2rem;
                margin-bottom: 1.5rem;
            }

            .register-form {
                padding: 20px;
            }

            .form-group {
                margin-bottom: 1rem;
            }

            .form-input {
                padding: 12px 15px;
                font-size: 1rem;
            }

            .btn-register {
                padding: 12px;
                font-size: 1rem;
            }

            .form-links {
                flex-direction: column;
                gap: 10px;
                text-align: center;
            }

            .form-links a {
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <div class="register-container">
        <h1 class="register-title"><a href="/" style="text-decoration: none; color: inherit;">🌸 注册</a></h1>
        
        {% if error %}
        <div class="error-message">{{ error }}</div>
        {% endif %}
        
        {% if success %}
        <div class="success-message">{{ success }}</div>
        {% endif %}
        
        <form method="POST">
            <div class="form-group">
                <label class="form-label" for="username">用户名</label>
                <input type="text" class="form-input" id="username" name="username" required>
            </div>
            
            <div class="form-group">
                <label class="form-label" for="password">密码</label>
                <input type="password" class="form-input" id="password" name="password" required>
            </div>
            
            <div class="form-group">
                <label class="form-label" for="confirm_password">确认密码</label>
                <input type="password" class="form-input" id="confirm_password" name="confirm_password" required>
            </div>
            
            <button type="submit" class="btn-register">注册</button>
        </form>
        
        <div class="register-links">
            <a href="/login" class="register-link">已有账号？登录</a>
            <a href="/" class="register-link">返回首页</a>
        </div>
    </div>
</body>
</html>'''

# 修改密码页面模板
CHANGE_PASSWORD_TEMPLATE = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修改密码 - 春色写真馆</title>
    <link rel="icon" type="image/svg+xml" href="/favicon.ico">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* 移除所有列表标记 */
        ul, ol { list-style: none; }
        li::marker { display: none; }

        body {
            font-family: 'PingFang SC', 'Hiragino Sans GB', 'Noto Sans CJK SC', 'Source Han Sans SC', 'Microsoft YaHei', '微软雅黑', sans-serif;
            background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 25%, #f0fff4 50%, #fff8dc 75%, #fdf5e6 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #5d4e37;
        }

        .change-password-container {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            padding: 3rem;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(255, 182, 193, 0.3);
            width: 100%;
            max-width: 450px;
            position: relative;
            overflow: hidden;
        }

        .change-password-container::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 182, 193, 0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .change-password-title {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 2rem;
            background: linear-gradient(45deg, #ff69b4, #ffa500, #98fb98);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            position: relative;
            z-index: 1;
        }

        .form-group {
            margin-bottom: 1.5rem;
            position: relative;
            z-index: 1;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            color: #8b4513;
            font-weight: bold;
        }

        .form-input {
            width: 100%;
            padding: 1rem;
            border: 2px solid rgba(255, 182, 193, 0.3);
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.8);
            color: #5d4e37;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #ff69b4;
            box-shadow: 0 0 15px rgba(255, 105, 180, 0.3);
        }

        .btn-change-password {
            width: 100%;
            padding: 1rem;
            background: linear-gradient(45deg, #ff69b4, #ffa500);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            z-index: 1;
        }

        .btn-change-password:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(255, 105, 180, 0.4);
        }

        .form-links {
            display: flex;
            justify-content: space-between;
            margin-top: 2rem;
            position: relative;
            z-index: 1;
        }

        .form-link {
            color: #8b4513;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .form-link:hover {
            color: #ff69b4;
        }

        .error-message {
            background: rgba(255, 0, 0, 0.1);
            color: #d32f2f;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1rem;
            text-align: center;
        }

        .success-message {
            background: rgba(0, 255, 0, 0.1);
            color: #2e7d32;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1rem;
            text-align: center;
        }

        .info-text {
            background: rgba(255, 193, 7, 0.1);
            color: #f57c00;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1rem;
            text-align: center;
            font-size: 0.9rem;
        }

        /* Mobile responsive styles */
        @media (max-width: 768px) {
            .change-password-container {
                padding: 20px 15px;
                margin: 10px;
                min-height: calc(100vh - 20px);
            }

            .change-password-title {
                font-size: 2rem;
                margin-bottom: 1.5rem;
            }

            .form-group {
                margin-bottom: 1rem;
            }

            .form-input {
                padding: 12px 15px;
                font-size: 1rem;
            }

            .btn-change-password {
                padding: 12px;
                font-size: 1rem;
            }

            .form-links {
                flex-direction: column;
                gap: 10px;
                text-align: center;
            }

            .form-links a {
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <div class="change-password-container">
        <h1 class="change-password-title"><a href="/" style="text-decoration: none; color: inherit;">🌸 修改密码</a></h1>

        {% if error %}
        <div class="error-message">{{ error }}</div>
        {% endif %}

        {% if success %}
        <div class="success-message">{{ success }}</div>
        {% endif %}

        <div class="info-text">
            💡 提示：可以修改任何用户的密码，无需验证当前密码。如果修改的是当前登录用户的密码，将自动退出登录。
        </div>

        <form method="POST" class="change-password-form">
            <div class="form-group">
                <label class="form-label" for="username">用户名</label>
                <input type="text" class="form-input" id="username" name="username" value="{{ username or '' }}" required>
            </div>

            <div class="form-group">
                <label class="form-label" for="new_password">新密码</label>
                <input type="password" class="form-input" id="new_password" name="new_password" required>
            </div>

            <div class="form-group">
                <label class="form-label" for="confirm_password">确认新密码</label>
                <input type="password" class="form-input" id="confirm_password" name="confirm_password" required>
            </div>

            <button type="submit" class="btn-change-password">修改密码</button>
        </form>

        <div class="form-links">
            <a href="/" class="form-link">返回首页</a>
            {% if session.user_id %}
            <a href="/logout" class="form-link">退出登录</a>
            {% else %}
            <a href="/login" class="form-link">登录</a>
            {% endif %}
        </div>
    </div>
</body>
</html>'''


ABOUT_TEMPLATE = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>关于我们 - 春色写真馆</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }

        /* 移除所有列表标记 */
        ul, ol { list-style: none; }
        li::marker { display: none; }

        body {
            font-family: 'PingFang SC', 'Hiragino Sans GB', 'Noto Sans CJK SC', 'Source Han Sans SC', 'Microsoft YaHei', '微软雅黑', sans-serif;
            background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 25%, #f0fff4 50%, #fff8dc 75%, #fdf5e6 100%);
            min-height: 100vh;
            color: #5d4e37;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
        }
        .header {
            text-align: center;
            margin-bottom: 3rem;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
        }
        .content {
            background: rgba(255, 255, 255, 0.9);
            padding: 2rem;
            border-radius: 20px;
            margin-bottom: 2rem;
        }
        h1 { color: #8b4513; margin-bottom: 1rem; }
        h2 { color: #cd853f; margin: 2rem 0 1rem 0; }
        .back-link {
            display: inline-block;
            padding: 0.8rem 1.5rem;
            background: linear-gradient(45deg, #ff69b4, #ffa500);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            transition: all 0.3s ease;
        }
        .back-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 105, 180, 0.4);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌸 关于春色写真馆</h1>
            <p>致力于提供优质的图片浏览和管理服务</p>
        </div>

        <div class="content">
            <h2>软件简介</h2>
            <p>春色写真馆是一个<strong>仅供本地使用</strong>的图片管理和浏览软件，专为个人用户在本地环境中管理和浏览图片而设计。</p>

            <h2>重要声明</h2>
            <div style="background: rgba(255, 99, 71, 0.1); padding: 1rem; border-radius: 10px; border-left: 4px solid #ff6347; margin: 1rem 0;">
                <strong>⚠️ 本软件仅限本地使用</strong>
                <ul>
                    <li>本软件设计用于个人本地环境，严禁部署到互联网服务器</li>
                    <li>禁止将本软件用于任何形式的网络服务或公开访问</li>
                    <li>用户需自行确保使用符合当地法律法规</li>
                </ul>
            </div>

            <h2>软件特色</h2>
            <ul>
                <li>🎨 精美的用户界面设计</li>
                <li>📸 多分类图片管理系统</li>
                <li>🔍 智能搜索和筛选功能</li>
                <li>❤️ 个性化收藏管理</li>
                <li>📱 响应式设计</li>
                <li>🔒 本地数据存储</li>
            </ul>

            <h2>开发者免责声明</h2>
            <div style="background: rgba(255, 182, 193, 0.1); padding: 1rem; border-radius: 10px; border-left: 4px solid #ff69b4; margin: 1rem 0;">
                <ul>
                    <li>开发者不对软件的任何误用承担责任</li>
                    <li>用户需自行承担使用风险和法律责任</li>
                    <li>开发者不提供任何形式的担保或保证</li>
                    <li>禁止商业使用或未经授权的分发</li>
                </ul>
            </div>

            <h2>使用限制</h2>
            <p>本软件仅供学习和个人使用，用户有责任确保其使用方式符合所在地区的法律法规。</p>
        </div>

        <div style="text-align: center;">
            <a href="/" class="back-link">🏠 返回首页</a>
        </div>
    </div>
</body>
</html>'''

CONTACT_TEMPLATE = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>联系方式 - 春色写真馆</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }

        /* 移除所有列表标记 */
        ul, ol { list-style: none; }
        li::marker { display: none; }

        body {
            font-family: 'PingFang SC', 'Hiragino Sans GB', 'Noto Sans CJK SC', 'Source Han Sans SC', 'Microsoft YaHei', '微软雅黑', sans-serif;
            background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 25%, #f0fff4 50%, #fff8dc 75%, #fdf5e6 100%);
            min-height: 100vh;
            color: #5d4e37;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
        }
        .header {
            text-align: center;
            margin-bottom: 3rem;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
        }
        .content {
            background: rgba(255, 255, 255, 0.9);
            padding: 2rem;
            border-radius: 20px;
            margin-bottom: 2rem;
        }
        h1 { color: #8b4513; margin-bottom: 1rem; }
        h2 { color: #cd853f; margin: 2rem 0 1rem 0; }
        .contact-item {
            background: rgba(255, 182, 193, 0.1);
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 10px;
            border-left: 4px solid #ff69b4;
        }
        .back-link {
            display: inline-block;
            padding: 0.8rem 1.5rem;
            background: linear-gradient(45deg, #ff69b4, #ffa500);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            transition: all 0.3s ease;
        }
        .back-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 105, 180, 0.4);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📞 联系方式</h1>
            <p>我们随时不为您提供帮助和支持</p>
        </div>

        <div class="content">
            <div style="background: rgba(255, 99, 71, 0.1); padding: 1rem; border-radius: 10px; border-left: 4px solid #ff6347; margin: 1rem 0;">
                <strong>⚠️ 重要提醒</strong><br>
                本软件为开源项目，且仅供本地使用。开发者不提供商业支持服务。
            </div>

            <h2>开源项目信息</h2>
            <div class="contact-item">
                <strong>📂 项目性质：</strong><br>
                开源软件项目，仅供学习和个人使用<br>
                <small>不提供商业技术支持</small>
            </div>

            <h2>社区支持</h2>
            <div class="contact-item">
                <strong>💬 社区讨论：</strong><br>
                不存在社区反馈，建议通过AI寻求帮助<br>
                <small>或用户可自行交流使用经验</small>
            </div>

            <h2>问题反馈</h2>
            <div class="contact-item">
                <strong>🐛 Bug报告：</strong><br>
                随便什么方式提交问题反馈<br>
                <small>开发者随缘查看</small>
            </div>

            <h2>免责声明</h2>
            <div style="background: rgba(255, 182, 193, 0.1); padding: 1rem; border-radius: 10px; border-left: 4px solid #ff69b4; margin: 1rem 0;">
                <ul>
                    <li>开发者不承担任何技术支持义务</li>
                    <li>软件按"现状"提供，无任何保证</li>
                    <li>用户自行承担使用风险</li>
                    <li>禁止用于商业用途</li>
                </ul>
            </div>

            <h2>使用建议</h2>
            <ul>
                <li>仔细阅读使用说明和注意事项</li>
                <li>确保在合法合规的环境下使用</li>
                <li>定期备份重要数据</li>
                <li>遇到问题可尝试重启软件</li>
            </ul>
        </div>

        <div style="text-align: center;">
            <a href="/" class="back-link">🏠 返回首页</a>
        </div>
    </div>
</body>
</html>'''

# 路由定义
@app.route('/favicon.ico')
def favicon():
    """Serve favicon as SVG"""
    from flask import Response
    return Response(FAVICON_SVG, mimetype='image/svg+xml')

@app.route('/')
def index():
    return render_template_string(MAIN_TEMPLATE, default_avatar=DEFAULT_AVATAR_SVG, placeholder_svg=PLACEHOLDER_SVG)



# ==================== ADMIN INTERFACE ====================

@app.route('/admin')
@login_required
def admin_panel():
    """管理面板"""
    return render_template_string(ADMIN_TEMPLATE)

@app.route('/api/admin/library-scan', methods=['POST'])
@login_required
def api_admin_library_scan():
    """手动触发图库扫描"""
    global admin_tasks

    if admin_tasks["library_scan"]["status"] == "running":
        return jsonify({'success': False, 'message': '扫描正在进行中'})

    # 重置状态
    admin_tasks["library_scan"] = {
        "status": "running",
        "progress": 0,
        "current": 0,
        "total": 0,
        "message": "开始扫描...",
        "last_run": None,
        "logs": []
    }

    # 启动扫描线程
    from threading import Thread
    import datetime

    def run_scan():
        try:
            admin_tasks["library_scan"]["logs"].append(f"[{datetime.datetime.now().strftime('%H:%M:%S')}] 开始图库扫描")
            scan_and_store_images_admin()
            admin_tasks["library_scan"]["status"] = "completed"
            admin_tasks["library_scan"]["last_run"] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            admin_tasks["library_scan"]["message"] = "扫描完成"
            admin_tasks["library_scan"]["logs"].append(f"[{datetime.datetime.now().strftime('%H:%M:%S')}] 扫描完成")
        except Exception as e:
            admin_tasks["library_scan"]["status"] = "error"
            admin_tasks["library_scan"]["message"] = f"扫描失败: {str(e)}"
            admin_tasks["library_scan"]["logs"].append(f"[{datetime.datetime.now().strftime('%H:%M:%S')}] 错误: {str(e)}")

    thread = Thread(target=run_scan)
    thread.daemon = True
    thread.start()

    return jsonify({'success': True, 'message': '扫描已开始'})

@app.route('/api/admin/path-fix', methods=['POST'])
@login_required
def api_admin_path_fix():
    """手动触发数据库路径修正"""
    global admin_tasks

    # 检查是否已经在运行
    if admin_tasks.get("path_fix", {}).get("status") == "running":
        return jsonify({'success': False, 'message': '路径修正正在进行中'})

    # 重置状态
    admin_tasks["path_fix"] = {
        "status": "running",
        "progress": 0,
        "current": 0,
        "total": 0,
        "message": "开始修正路径...",
        "last_run": None,
        "logs": []
    }

    # 启动修正线程
    from threading import Thread
    import datetime

    def run_path_fix():
        try:
            admin_tasks["path_fix"]["logs"].append(f"[{datetime.datetime.now().strftime('%H:%M:%S')}] 开始路径修正")

            conn = sqlite3.connect('gallery.db')
            c = conn.cursor()

            # 查找所有不以 /images/ 开头的路径
            c.execute("SELECT id, path, full_path FROM images WHERE path NOT LIKE '/images/%'")
            old_paths = c.fetchall()

            admin_tasks["path_fix"]["total"] = len(old_paths)

            if old_paths:
                admin_tasks["path_fix"]["logs"].append(f"发现 {len(old_paths)} 条旧格式路径，正在修正...")

                import urllib.parse
                for i, (image_id, old_path, old_full_path) in enumerate(old_paths):
                    # 生成新的路径格式
                    if old_path.startswith('/'):
                        # 如果已经是绝对路径但不是 /images/ 开头，添加 /images 前缀
                        new_path = f"/images{old_path}"
                        new_full_path = f"/images{old_full_path}" if old_full_path else new_path
                    else:
                        # 如果是相对路径，添加 /images/ 前缀
                        new_path = f"/images/{old_path}"
                        new_full_path = f"/images/{old_full_path}" if old_full_path else new_path

                    # 更新数据库
                    c.execute("UPDATE images SET path = ?, full_path = ? WHERE id = ?",
                             (new_path, new_full_path, image_id))

                    admin_tasks["path_fix"]["current"] = i + 1
                    admin_tasks["path_fix"]["progress"] = int(100 * (i + 1) / len(old_paths))

                conn.commit()
                admin_tasks["path_fix"]["logs"].append(f"✅ 已修正 {len(old_paths)} 条路径格式")
            else:
                admin_tasks["path_fix"]["logs"].append("✅ 所有路径格式都是最新的")

            conn.close()

            admin_tasks["path_fix"]["status"] = "completed"
            admin_tasks["path_fix"]["last_run"] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            admin_tasks["path_fix"]["message"] = "路径修正完成"
            admin_tasks["path_fix"]["logs"].append(f"[{datetime.datetime.now().strftime('%H:%M:%S')}] 路径修正完成")
        except Exception as e:
            admin_tasks["path_fix"]["status"] = "error"
            admin_tasks["path_fix"]["message"] = f"路径修正失败: {str(e)}"
            admin_tasks["path_fix"]["logs"].append(f"[{datetime.datetime.now().strftime('%H:%M:%S')}] 错误: {str(e)}")

    thread = Thread(target=run_path_fix)
    thread.daemon = True
    thread.start()

    return jsonify({'success': True, 'message': '路径修正已开始'})

@app.route('/api/admin/filename-fix', methods=['POST'])
@login_required
def api_admin_filename_fix():
    """手动触发文件名修复"""
    global admin_tasks

    if admin_tasks["filename_fix"]["status"] == "running":
        return jsonify({'success': False, 'message': '文件名修复正在进行中'})

    # 重置状态
    admin_tasks["filename_fix"] = {
        "status": "running",
        "progress": 0,
        "current": 0,
        "total": 0,
        "message": "开始检查文件名...",
        "last_run": None,
        "logs": []
    }

    # 启动修复线程
    from threading import Thread
    import datetime

    def run_fix():
        try:
            admin_tasks["filename_fix"]["logs"].append(f"[{datetime.datetime.now().strftime('%H:%M:%S')}] 开始文件名修复")
            fix_shit_encoded_files_admin()
            admin_tasks["filename_fix"]["status"] = "completed"
            admin_tasks["filename_fix"]["last_run"] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            admin_tasks["filename_fix"]["message"] = "修复完成"
            admin_tasks["filename_fix"]["logs"].append(f"[{datetime.datetime.now().strftime('%H:%M:%S')}] 修复完成")
        except Exception as e:
            admin_tasks["filename_fix"]["status"] = "error"
            admin_tasks["filename_fix"]["message"] = f"修复失败: {str(e)}"
            admin_tasks["filename_fix"]["logs"].append(f"[{datetime.datetime.now().strftime('%H:%M:%S')}] 错误: {str(e)}")

    thread = Thread(target=run_fix)
    thread.daemon = True
    thread.start()

    return jsonify({'success': True, 'message': '文件名修复已开始'})

@app.route('/api/admin/status')
@login_required
def api_admin_status():
    """获取管理任务状态"""
    return jsonify(admin_tasks)

@app.route('/api/album-favorite', methods=['POST'])
@login_required
def api_toggle_album_favorite():
    """切换图集收藏状态"""
    user_id = session['user_id']
    data = request.get_json()
    category = data.get('category')
    album = data.get('album')

    if not category or not album:
        return jsonify({'success': False, 'message': '参数不完整'})

    conn = sqlite3.connect('gallery.db')
    c = conn.cursor()

    # 检查是否已收藏
    c.execute('SELECT id FROM album_favorites WHERE user_id = ? AND category = ? AND album = ?',
              (user_id, category, album))
    existing = c.fetchone()

    if existing:
        # 取消收藏
        c.execute('DELETE FROM album_favorites WHERE user_id = ? AND category = ? AND album = ?',
                  (user_id, category, album))
        is_favorited = False
        message = '已取消收藏图集'
    else:
        # 添加收藏
        c.execute('INSERT INTO album_favorites (user_id, category, album) VALUES (?, ?, ?)',
                  (user_id, category, album))
        is_favorited = True
        message = '已收藏图集'

    conn.commit()
    conn.close()

    return jsonify({
        'success': True,
        'is_favorited': is_favorited,
        'message': message
    })

@app.route('/api/album-favorite-status')
@login_required
def api_album_favorite_status():
    """检查图集收藏状态"""
    user_id = session['user_id']
    category = request.args.get('category')
    album = request.args.get('album')

    if not category or not album:
        return jsonify({'is_favorited': False})

    conn = sqlite3.connect('gallery.db')
    c = conn.cursor()

    c.execute('SELECT id FROM album_favorites WHERE user_id = ? AND category = ? AND album = ?',
              (user_id, category, album))
    is_favorited = c.fetchone() is not None

    conn.close()

    return jsonify({'is_favorited': is_favorited})

@app.route('/api/favorited-albums')
@login_required
def api_favorited_albums():
    """获取用户收藏的图集"""
    user_id = session['user_id']
    page = int(request.args.get('page', 1))
    limit = int(request.args.get('limit', 16))  # 与主页一致
    offset = (page - 1) * limit
    category = request.args.get('category', '')
    sort = request.args.get('sort', 'newest')

    conn = sqlite3.connect('gallery.db')
    c = conn.cursor()

    # 构建查询条件
    where_conditions = ['album_favorites.user_id = ?']
    params = [user_id]

    if category:
        where_conditions.append('album_favorites.category = ?')
        params.append(category)

    where_clause = ' AND '.join(where_conditions)

    # 构建排序条件
    if sort == 'oldest':
        order_clause = 'ORDER BY album_favorites.created_at ASC'
    elif sort == 'name':
        order_clause = 'ORDER BY album_favorites.album ASC'
    else:  # newest
        order_clause = 'ORDER BY album_favorites.created_at DESC'

    # 获取收藏的图集信息
    query = '''SELECT album_favorites.category, album_favorites.album, album_favorites.created_at,
                      COUNT(images.id) as image_count,
                      MIN(images.path) as cover_image
               FROM album_favorites
               LEFT JOIN images ON album_favorites.category = images.category AND album_favorites.album = images.album
               WHERE ''' + where_clause + '''
               GROUP BY album_favorites.category, album_favorites.album
               ''' + order_clause + '''
               LIMIT ? OFFSET ?'''

    c.execute(query, params + [limit, offset])
    results = c.fetchall()

    # 获取总数
    count_query = 'SELECT COUNT(*) FROM album_favorites WHERE ' + where_clause
    c.execute(count_query, params)
    total_count = c.fetchone()[0]

    conn.close()

    albums = []
    for row in results:
        albums.append({
            'category': row[0],
            'album': row[1],
            'created_at': row[2],
            'image_count': row[3],
            'cover_image': row[4] or PLACEHOLDER_SVG
        })

    return jsonify({
        'albums': albums,
        'total': total_count,
        'page': page,
        'limit': limit,
        'total_pages': (total_count + limit - 1) // limit
    })

@app.route('/api/favorites-grouped')
@login_required
def api_favorites_grouped():
    """获取按图集分组的收藏图片"""
    user_id = session['user_id']
    page = int(request.args.get('page', 1))
    limit = int(request.args.get('limit', 12))
    offset = (page - 1) * limit

    conn = sqlite3.connect('gallery.db')
    c = conn.cursor()

    # 获取收藏图片按图集分组
    c.execute('''SELECT i.category, i.album, COUNT(f.id) as favorite_count,
                        MIN(i.path) as cover_image, MAX(f.created_at) as latest_favorited
                 FROM favorites f
                 JOIN images i ON f.image_id = i.id
                 WHERE f.user_id = ?
                 GROUP BY i.category, i.album
                 ORDER BY latest_favorited DESC
                 LIMIT ? OFFSET ?''', (user_id, limit, offset))

    results = c.fetchall()

    # 获取总数
    c.execute('''SELECT COUNT(*)
                 FROM (SELECT DISTINCT i.category, i.album
                       FROM favorites f
                       JOIN images i ON f.image_id = i.id
                       WHERE f.user_id = ?)''', (user_id,))
    total_count = c.fetchone()[0]

    conn.close()

    albums = []
    for row in results:
        albums.append({
            'category': row[0],
            'album': row[1],
            'favorite_count': row[2],
            'cover_image': row[3],
            'latest_favorited': row[4]
        })

    return jsonify({
        'albums': albums,
        'total': total_count,
        'page': page,
        'limit': limit,
        'total_pages': (total_count + limit - 1) // limit
    })

@app.route('/api/clear-album-favorites', methods=['POST'])
@login_required
def api_clear_album_favorites():
    """清空用户的所有图集收藏"""
    user_id = session['user_id']

    conn = sqlite3.connect('gallery.db')
    c = conn.cursor()

    c.execute('DELETE FROM album_favorites WHERE user_id = ?', (user_id,))
    deleted_count = c.rowcount

    conn.commit()
    conn.close()

    return jsonify({
        'success': True,
        'message': f'已清空 {deleted_count} 个图集收藏'
    })

@app.route('/api/album-favorites-only/<category>/<album>')
@login_required
def api_album_favorites_only(category, album):
    """获取指定图集中用户收藏的图片"""
    user_id = session['user_id']
    page = int(request.args.get('page', 1))
    limit = int(request.args.get('limit', 20))
    offset = (page - 1) * limit

    conn = sqlite3.connect('gallery.db')
    c = conn.cursor()

    # 获取该图集中用户收藏的图片 - 返回完整数据结构
    c.execute('''SELECT i.id, i.filename, i.path, i.full_path, i.file_size,
                        i.category, i.album, i.created_at, f.created_at as favorited_at
                 FROM favorites f
                 JOIN images i ON f.image_id = i.id
                 WHERE f.user_id = ? AND i.category = ? AND i.album = ?
                 ORDER BY f.created_at DESC
                 LIMIT ? OFFSET ?''',
              (user_id, category, album, limit, offset))

    results = c.fetchall()

    # 获取总数
    c.execute('''SELECT COUNT(*)
                 FROM favorites f
                 JOIN images i ON f.image_id = i.id
                 WHERE f.user_id = ? AND i.category = ? AND i.album = ?''',
              (user_id, category, album))
    total_count = c.fetchone()[0]

    conn.close()

    images = []
    for row in results:
        images.append({
            'id': row[0],
            'filename': row[1],
            'path': row[2],
            'full_path': row[3],
            'file_size': row[4],
            'category': row[5],
            'album': row[6],
            'created_at': row[7],
            'favorited_at': row[8]
        })

    return jsonify({
        'images': images,
        'total': total_count,
        'page': page,
        'limit': limit,
        'total_pages': (total_count + limit - 1) // limit if total_count > 0 else 0
    })

@app.route('/album/<category>/<album>')
def album_detail(category, album):
    category_name = get_category_name(category)
    # 检查是否只显示收藏图片
    favorite_only = request.args.get('favorite', 'false').lower() == 'true'

    return render_template_string(ALBUM_TEMPLATE,
                                 category=category,
                                 album=album,
                                 category_name=category_name,
                                 favorite_only=favorite_only,
                                 placeholder_svg=PLACEHOLDER_SVG)

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        conn = sqlite3.connect('gallery.db')
        c = conn.cursor()

        # 检查表结构，决定使用哪种查询
        c.execute("PRAGMA table_info(users)")
        columns = [column[1] for column in c.fetchall()]

        if 'email' in columns:
            # 旧表结构，支持用户名或邮箱登录
            c.execute('SELECT id, username, password_hash, avatar FROM users WHERE username = ? OR email = ?',
                      (username, username))
        else:
            # 新表结构，只支持用户名登录
            c.execute('SELECT id, username, password_hash, avatar FROM users WHERE username = ?',
                      (username,))

        user = c.fetchone()
        conn.close()
        
        if user and check_password_hash(user[2], password):
            session['user_id'] = user[0]
            session['username'] = user[1]
            session['avatar'] = user[3] or DEFAULT_AVATAR_SVG
            return redirect(url_for('index'))
        else:
            return render_template_string(LOGIN_TEMPLATE, error='用户名或密码错误')
    
    return render_template_string(LOGIN_TEMPLATE)

@app.route('/register', methods=['GET', 'POST'])
def register():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        confirm_password = request.form['confirm_password']

        if password != confirm_password:
            return render_template_string(REGISTER_TEMPLATE, error='两次输入的密码不一致')

        if len(password) < 6:
            return render_template_string(REGISTER_TEMPLATE, error='密码长度至少6位')

        conn = None
        try:
            conn = sqlite3.connect('gallery.db', timeout=10.0)
            c = conn.cursor()

            # 检查用户名是否已存在
            c.execute('SELECT id FROM users WHERE username = ?', (username,))
            if c.fetchone():
                return render_template_string(REGISTER_TEMPLATE, error='用户名已存在')

            # 创建新用户（兼容现有数据库结构）
            password_hash = generate_password_hash(password)

            # 首先检查表结构
            c.execute("PRAGMA table_info(users)")
            columns = [column[1] for column in c.fetchall()]

            if 'email' in columns:
                # 表包含email字段，使用NULL值
                c.execute('INSERT INTO users (username, email, password_hash, avatar) VALUES (?, ?, ?, ?)',
                         (username, None, password_hash, DEFAULT_AVATAR_SVG))
            else:
                # 表不包含email字段
                c.execute('INSERT INTO users (username, password_hash, avatar) VALUES (?, ?, ?)',
                         (username, password_hash, DEFAULT_AVATAR_SVG))

            user_id = c.lastrowid
            conn.commit()

        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            return render_template_string(REGISTER_TEMPLATE, error=f'注册失败: {str(e)}')
        finally:
            if conn:
                conn.close()

        # BUG FIX: 自动登录新注册的用户并重定向到主页
        session['user_id'] = user_id
        session['username'] = username
        session['avatar'] = DEFAULT_AVATAR_SVG
        return redirect(url_for('index'))

    return render_template_string(REGISTER_TEMPLATE)

@app.route('/change-password', methods=['GET', 'POST'])
def change_password():
    if request.method == 'POST':
        username = request.form['username']
        new_password = request.form['new_password']
        confirm_password = request.form['confirm_password']

        if new_password != confirm_password:
            return render_template_string(CHANGE_PASSWORD_TEMPLATE,
                                        error='两次输入的密码不一致',
                                        username=username)

        if len(new_password) < 6:
            return render_template_string(CHANGE_PASSWORD_TEMPLATE,
                                        error='密码长度至少6位',
                                        username=username)

        conn = sqlite3.connect('gallery.db')
        c = conn.cursor()

        # 检查用户是否存在
        c.execute('SELECT id FROM users WHERE username = ?', (username,))
        user = c.fetchone()

        if not user:
            conn.close()
            return render_template_string(CHANGE_PASSWORD_TEMPLATE,
                                        error='用户不存在',
                                        username=username)

        # 更新密码
        password_hash = generate_password_hash(new_password)
        c.execute('UPDATE users SET password_hash = ? WHERE username = ?',
                  (password_hash, username))
        conn.commit()
        conn.close()

        # 如果修改的是当前登录用户的密码，自动退出登录
        current_user = session.get('username')
        if current_user == username:
            session.clear()
            return render_template_string(CHANGE_PASSWORD_TEMPLATE,
                                        success='密码修改成功！由于修改了当前用户密码，已自动退出登录，请重新登录。')
        else:
            return render_template_string(CHANGE_PASSWORD_TEMPLATE,
                                        success=f'用户 {username} 的密码修改成功！')

    # GET request - 检查是否从忘记密码链接过来
    username = request.args.get('username', '')
    return render_template_string(CHANGE_PASSWORD_TEMPLATE, username=username)

@app.route('/logout')
def logout():
    session.clear()
    return redirect(url_for('index'))

@app.route('/images/<path:filename>')
def serve_image(filename):
    # COMPREHENSIVE FIX: 处理所有URL编码问题，包括多重编码和特殊字符
    import urllib.parse
    import re

    def comprehensive_decode_attempts(text):
        """生成所有可能的解码尝试，包括各种编码组合"""
        attempts = set()  # 使用set自动去重

        # 1. 原始文件名
        attempts.add(text)

        # 2. 标准URL解码（多次）
        current = text
        for i in range(5):  # 最多5次解码
            try:
                decoded = urllib.parse.unquote(current, encoding='utf-8')
                attempts.add(decoded)
                if decoded == current:
                    break
                current = decoded
            except:
                break

        # 3. GBK编码尝试
        try:
            gbk_decoded = urllib.parse.unquote(text, encoding='gbk')
            attempts.add(gbk_decoded)
            # GBK解码后再进行多次URL解码
            current = gbk_decoded
            for i in range(3):
                try:
                    decoded = urllib.parse.unquote(current, encoding='utf-8')
                    attempts.add(decoded)
                    if decoded == current:
                        break
                    current = decoded
                except:
                    break
        except:
            pass

        # 4. 特殊模式处理：%25XX -> %XX -> 字符
        if '%25' in text:
            # 处理双重编码的%符号
            temp_text = text.replace('%25', '%')
            attempts.add(temp_text)
            # 对替换后的文本再进行解码
            try:
                decoded = urllib.parse.unquote(temp_text, encoding='utf-8')
                attempts.add(decoded)
            except:
                pass

        # 5. 特殊处理：%2528 和 %2529 模式
        if '%2528' in text or '%2529' in text:
            variations = [text]
            for var in list(variations):
                if '%2528' in var:
                    variations.append(var.replace('%2528', '%28'))
                if '%2529' in var:
                    variations.append(var.replace('%2529', '%29'))
                if '%2528' in var and '%2529' in var:
                    variations.append(var.replace('%2528', '%28').replace('%2529', '%29'))

            for var in variations:
                attempts.add(var)
                try:
                    decoded = urllib.parse.unquote(var, encoding='utf-8')
                    attempts.add(decoded)
                except:
                    pass

        # 6. 混合解码：只解码目录部分，保持文件名编码
        if '/' in text and '%' in text:
            parts = text.split('/')
            filename_part = parts[-1]
            dir_parts = parts[:-1]

            # 解码目录部分
            try:
                decoded_dirs = []
                for part in dir_parts:
                    decoded_dirs.append(urllib.parse.unquote(part, encoding='utf-8'))
                mixed_path = '/'.join(decoded_dirs) + '/' + filename_part
                attempts.add(mixed_path)
            except:
                pass

        # 7. 处理+号编码问题
        if '%2B' in text:
            plus_replaced = text.replace('%2B', '+')
            attempts.add(plus_replaced)
            try:
                decoded = urllib.parse.unquote(plus_replaced, encoding='utf-8')
                attempts.add(decoded)
            except:
                pass

        # 8. 处理空格编码问题
        if '%20' in text:
            space_replaced = text.replace('%20', ' ')
            attempts.add(space_replaced)

        # 转换为列表并返回
        return list(attempts)

    # 生成所有可能的解码尝试
    decode_attempts = comprehensive_decode_attempts(filename)

    # 按优先级排序：原始文件名优先，然后是较少解码的版本
    def sort_key(path):
        # 原始文件名优先级最高
        if path == filename:
            return 0
        # 包含%编码的优先级较高（可能是实际存储的文件名）
        elif '%' in path:
            return 1
        # 其他按长度排序
        else:
            return 2 + len(path)

    decode_attempts.sort(key=sort_key)

    # 尝试每种解码结果
    for attempt in decode_attempts:
        try:
            # 安全路径检查
            safe_path = os.path.normpath(os.path.join('downloaded', attempt))

            # 确保路径在downloaded目录内
            if not safe_path.startswith(os.path.normpath('downloaded') + os.sep):
                continue

            # 检查文件是否存在
            if os.path.exists(safe_path):
                # 成功找到文件
                return send_from_directory('downloaded', attempt)

        except Exception as e:
            # 忽略路径处理错误，继续尝试下一个
            continue

    # 所有尝试都失败，记录调试信息
    print(f"[404] 文件未找到: {filename}")
    print(f"[404] 尝试了 {len(decode_attempts)} 种解码方式")
    if len(decode_attempts) <= 10:
        print(f"[404] 尝试列表: {decode_attempts}")
    else:
        print(f"[404] 前10个尝试: {decode_attempts[:10]}")

    return "Not Found", 404

@app.route('/api/scan-progress')
def api_scan_progress():
    return jsonify(scan_progress)

@app.route('/api/images')
def api_images():
    category = request.args.get('category')
    search = request.args.get('search')
    sort = request.args.get('sort', 'newest')
    page = int(request.args.get('page', 1))
    limit = int(request.args.get('limit', 50))
    offset = (page - 1) * limit
    
    images = get_images_from_db(category=category, search=search, limit=limit, offset=offset)
    
    # 简单的排序逻辑
    if sort == 'popular':
        random.shuffle(images)
    elif sort == 'rating':
        random.shuffle(images)
    
    return jsonify(images)

@app.route('/api/albums')
def api_albums():
    category = request.args.get('category')
    search = request.args.get('search')
    sort = request.args.get('sort', 'newest')
    page = int(request.args.get('page', 1))
    limit = int(request.args.get('limit', 16))  # 16 albums per page
    offset = (page - 1) * limit

    albums = get_albums_from_db(category=category)

    # 搜索过滤
    if search:
        albums = [album for album in albums if search.lower() in album['album'].lower()]

    # 排序
    if sort == 'count':
        albums.sort(key=lambda x: x['image_count'], reverse=True)
    elif sort == 'popular':
        random.shuffle(albums)

    # 分页
    total_albums = len(albums)
    paginated_albums = albums[offset:offset + limit]

    return jsonify({
        'albums': paginated_albums,
        'total': total_albums,
        'page': page,
        'limit': limit,
        'total_pages': (total_albums + limit - 1) // limit
    })

@app.route('/api/album-images')
def api_album_images():
    category = request.args.get('category')
    album = request.args.get('album')
    page = int(request.args.get('page', 1))
    limit = int(request.args.get('limit', 20))
    offset = (page - 1) * limit
    
    if not category or not album:
        return jsonify({'error': 'Missing parameters'}), 400
    
    images = get_images_from_db(category=category, album=album, limit=limit, offset=offset)
    
    # 获取总数
    conn = sqlite3.connect('gallery.db')
    c = conn.cursor()
    c.execute('SELECT COUNT(*) FROM images WHERE category = ? AND album = ?', (category, album))
    total = c.fetchone()[0]
    conn.close()
    
    return jsonify({
        'images': images,
        'total': total,
        'page': page,
        'limit': limit
    })

@app.route('/api/album/<category>/<album>/images')
def api_album_images_enhanced(category, album):
    """增强版图片API，支持元数据模式和批量加载"""
    metadata_only = request.args.get('metadata_only', 'false').lower() == 'true'
    start = int(request.args.get('start', 0))
    limit = int(request.args.get('limit', 0))

    conn = sqlite3.connect('gallery.db')
    c = conn.cursor()

    if metadata_only:
        # 仅返回元数据（id, filename, path等基本信息）
        c.execute('''
            SELECT id, filename, path, category, album, created_at
            FROM images
            WHERE category = ? AND album = ?
            ORDER BY created_at DESC
        ''', (category, album))

        images = []
        for row in c.fetchall():
            images.append({
                'id': row[0],
                'filename': row[1],
                'path': row[2],
                'category': row[3],
                'album': row[4],
                'created_at': row[5]
            })

        conn.close()
        return jsonify({
            'images': images,
            'total': len(images),
            'metadata_only': True
        })

    elif limit > 0:
        # 批量加载模式
        c.execute('''
            SELECT id, filename, path, category, album, created_at, file_size
            FROM images
            WHERE category = ? AND album = ?
            ORDER BY created_at DESC
            LIMIT ? OFFSET ?
        ''', (category, album, limit, start))

        images = []
        for row in c.fetchall():
            images.append({
                'id': row[0],
                'filename': row[1],
                'path': row[2],
                'category': row[3],
                'album': row[4],
                'created_at': row[5],
                'file_size': row[6]
            })

        conn.close()
        return jsonify({
            'images': images,
            'start': start,
            'limit': limit,
            'count': len(images)
        })

    else:
        # 默认模式，返回所有图片
        images = get_images_from_db(category=category, album=album)
        conn.close()
        return jsonify({
            'images': images,
            'total': len(images)
        })

@app.route('/api/stats')
def api_stats():
    conn = sqlite3.connect('gallery.db')
    c = conn.cursor()
    
    stats = {}
    categories = ['korea', 'cosplay', 'japan', 'gravure', 'chinese', 'thailand']
    
    for category in categories:
        c.execute('SELECT COUNT(*) FROM images WHERE category = ?', (category,))
        image_count = c.fetchone()[0]
        
        c.execute('SELECT COUNT(DISTINCT album) FROM images WHERE category = ?', (category,))
        album_count = c.fetchone()[0]
        
        stats[category] = {
            'albums': album_count,
            'images': image_count
        }
    
    conn.close()
    return jsonify(stats)

@app.route('/api/user-favorites')
@login_required
def api_user_favorites():
    """获取用户收藏的图片ID列表"""
    user_id = session['user_id']
    
    conn = sqlite3.connect('gallery.db')
    c = conn.cursor()
    c.execute('SELECT image_id FROM favorites WHERE user_id = ?', (user_id,))
    favorites = [{'image_id': row[0]} for row in c.fetchall()]
    conn.close()
    
    return jsonify(favorites)

@app.route('/api/favorite', methods=['POST'])
@login_required
def api_favorite():
    data = request.get_json()
    image_id = data.get('image_id')
    user_id = session['user_id']
    
    if not image_id:
        return jsonify({'success': False, 'error': 'Image ID is required'}), 400

    conn = sqlite3.connect('gallery.db')
    c = conn.cursor()
    
    # 检查是否已收藏
    c.execute('SELECT id FROM favorites WHERE user_id = ? AND image_id = ?', (user_id, image_id))
    existing = c.fetchone()
    
    if existing:
        # 取消收藏
        c.execute('DELETE FROM favorites WHERE id = ?', (existing[0],))
        favorited = False
    else:
        # 添加收藏
        c.execute('INSERT INTO favorites (user_id, image_id) VALUES (?, ?)', (user_id, image_id))
        favorited = True
    
    conn.commit()
    conn.close()
    
    return jsonify({'success': True, 'favorited': favorited})

@app.route('/api/favorite-album-all', methods=['POST'])
@login_required
def api_favorite_album_all():
    """批量收藏图集中的所有图片"""
    user_id = session['user_id']
    data = request.get_json()
    category = data.get('category')
    album = data.get('album')

    if not category or not album:
        return jsonify({'success': False, 'message': '参数不完整'})

    conn = sqlite3.connect('gallery.db')
    c = conn.cursor()

    try:
        # 获取图集中的所有图片ID
        c.execute('SELECT id FROM images WHERE category = ? AND album = ?', (category, album))
        image_ids = [row[0] for row in c.fetchall()]

        if not image_ids:
            return jsonify({'success': False, 'message': '图集中没有找到图片'})

        # 批量插入收藏记录（忽略已存在的）
        favorited_ids = []
        for image_id in image_ids:
            try:
                c.execute('INSERT OR IGNORE INTO favorites (user_id, image_id) VALUES (?, ?)',
                         (user_id, image_id))
                if c.rowcount > 0:  # 新插入的记录
                    favorited_ids.append(image_id)
            except sqlite3.Error:
                continue

        conn.commit()

        return jsonify({
            'success': True,
            'count': len(favorited_ids),
            'total_images': len(image_ids),
            'favorited_ids': favorited_ids,
            'message': f'成功收藏了 {len(favorited_ids)} 张图片'
        })

    except Exception as e:
        conn.rollback()
        return jsonify({'success': False, 'message': f'批量收藏失败: {str(e)}'})
    finally:
        conn.close()

@app.route('/api/unfavorite-album-all', methods=['POST'])
@login_required
def api_unfavorite_album_all():
    """批量取消收藏图集中的所有图片"""
    user_id = session['user_id']
    data = request.get_json()
    category = data.get('category')
    album = data.get('album')

    if not category or not album:
        return jsonify({'success': False, 'message': '参数不完整'})

    conn = sqlite3.connect('gallery.db')
    c = conn.cursor()

    try:
        # 获取图集中的所有图片ID
        c.execute('SELECT id FROM images WHERE category = ? AND album = ?', (category, album))
        image_ids = [row[0] for row in c.fetchall()]

        if not image_ids:
            return jsonify({'success': False, 'message': '图集中没有找到图片'})

        # 批量删除收藏记录
        placeholders = ','.join(['?' for _ in image_ids])
        c.execute(f'DELETE FROM favorites WHERE user_id = ? AND image_id IN ({placeholders})',
                 [user_id] + image_ids)

        removed_count = c.rowcount
        conn.commit()

        return jsonify({
            'success': True,
            'count': removed_count,
            'message': f'成功取消收藏 {removed_count} 张图片'
        })

    except Exception as e:
        conn.rollback()
        return jsonify({'success': False, 'message': f'取消收藏失败: {str(e)}'})
    finally:
        conn.close()

# BUG FIX: 重写此路由以实现高效的分页、过滤和排序，支持图片收藏
@app.route('/api/favorites')
@login_required
def api_favorites():
    user_id = session['user_id']
    request_type = request.args.get('type', 'albums')  # 'albums', 'images', 'images_grouped'
    category = request.args.get('category')
    sort = request.args.get('sort', 'newest')
    page = int(request.args.get('page', 1))
    limit = int(request.args.get('limit', 12))
    offset = (page - 1) * limit

    if request_type == 'images':
        # 返回收藏的图片（平铺显示）
        favorites, total_count = get_user_favorite_images(
            user_id=user_id,
            category=category,
            sort=sort,
            limit=limit,
            offset=offset
        )

        return jsonify({
            'favorites': favorites,
            'total': total_count,
            'page': page,
            'total_pages': math.ceil(total_count / limit) if total_count > 0 else 1
        })

    elif request_type == 'images_grouped':
        # 返回按图集分组的收藏图片
        groups, total_count = get_user_favorite_images_grouped(
            user_id=user_id,
            category=category,
            sort=sort,
            limit=limit,
            offset=offset
        )

        return jsonify({
            'groups': groups,
            'total': total_count,
            'page': page,
            'total_pages': math.ceil(total_count / limit) if total_count > 0 else 1
        })

    else:
        # 默认返回收藏的图集
        favorites, total_count = get_user_favorites(
            user_id=user_id,
            category=category,
            sort=sort,
            limit=limit,
            offset=offset
        )

        return jsonify({
            'favorites': favorites,
            'total': total_count,
            'page': page,
            'limit': limit
        })


@app.route('/api/favorites-stats')
@login_required
def api_favorites_stats():
    user_id = session['user_id']

    conn = sqlite3.connect('gallery.db')
    c = conn.cursor()

    # === 图片收藏统计 ===
    # 总图片收藏数
    c.execute('SELECT COUNT(id) FROM favorites WHERE user_id = ?', (user_id,))
    total_images = c.fetchone()[0]

    # 图片涉及分类数
    c.execute('''SELECT COUNT(DISTINCT i.category)
                  FROM favorites f
                  JOIN images i ON f.image_id = i.id
                  WHERE f.user_id = ?''', (user_id,))
    image_categories = c.fetchone()[0]

    # 图片涉及图集数
    c.execute('''SELECT COUNT(DISTINCT i.album)
                  FROM favorites f
                  JOIN images i ON f.image_id = i.id
                  WHERE f.user_id = ?''', (user_id,))
    image_albums = c.fetchone()[0]

    # 图片本周新增（最近7天）
    seven_days_ago = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d %H:%M:%S')
    c.execute('''SELECT COUNT(id)
                  FROM favorites
                  WHERE user_id = ? AND created_at >= ?''', (user_id, seven_days_ago))
    recent_images = c.fetchone()[0]

    # === 图集收藏统计 ===
    # 总图集收藏数
    c.execute('SELECT COUNT(id) FROM album_favorites WHERE user_id = ?', (user_id,))
    total_albums = c.fetchone()[0]

    # 图集涉及分类数
    c.execute('''SELECT COUNT(DISTINCT category)
                  FROM album_favorites
                  WHERE user_id = ?''', (user_id,))
    album_categories = c.fetchone()[0]

    # 图集本周新增（最近7天）
    c.execute('''SELECT COUNT(id)
                  FROM album_favorites
                  WHERE user_id = ? AND created_at >= ?''', (user_id, seven_days_ago))
    recent_albums = c.fetchone()[0]

    conn.close()

    return jsonify({
        # 图片收藏统计
        'images': {
            'total': total_images,
            'categories': image_categories,
            'albums': image_albums,
            'recent': recent_images
        },
        # 图集收藏统计
        'albums': {
            'total': total_albums,
            'categories': album_categories,
            'recent': recent_albums
        },
        # 总计统计
        'total': {
            'favorites': total_images + total_albums,
            'categories': max(image_categories, album_categories),
            'recent': recent_images + recent_albums
        }
    })



@app.route('/api/favorites/clear', methods=['POST'])
@login_required
def api_clear_favorites():
    user_id = session['user_id']
    
    conn = sqlite3.connect('gallery.db')
    c = conn.cursor()
    c.execute('DELETE FROM favorites WHERE user_id = ?', (user_id,))
    conn.commit()
    conn.close()
    
    return jsonify({'success': True})

@app.route('/api/comments', methods=['GET', 'POST'])
def api_comments():
    if request.method == 'POST':
        if 'user_id' not in session:
            return jsonify({'success': False, 'error': '请先登录'}), 401
        
        data = request.get_json()
        image_id = data.get('image_id')
        content = data.get('content')
        user_id = session['user_id']

        if not all([image_id, content]):
            return jsonify({'success': False, 'error': '缺少必要参数'}), 400
        
        conn = sqlite3.connect('gallery.db')
        c = conn.cursor()
        c.execute('INSERT INTO comments (user_id, image_id, content) VALUES (?, ?, ?)',
                 (user_id, image_id, content))
        conn.commit()
        conn.close()
        
        return jsonify({'success': True})
    
    else: # GET
        image_id = request.args.get('image_id')
        if not image_id:
            return jsonify([])

        conn = sqlite3.connect('gallery.db')
        c = conn.cursor()
        c.execute('''SELECT c.content, c.created_at, u.username, u.avatar
                      FROM comments c
                      JOIN users u ON c.user_id = u.id
                      WHERE c.image_id = ?
                      ORDER BY c.created_at DESC''', (image_id,))
        comments = c.fetchall()
        conn.close()
        
        return jsonify([{
            'content': comment[0],
            'created_at': comment[1],
            'username': comment[2],
            'avatar': comment[3]
        } for comment in comments])

@app.route('/favorites')
@login_required
def favorites():
    return render_template_string(FAVORITES_TEMPLATE, default_avatar=DEFAULT_AVATAR_SVG, placeholder_svg=PLACEHOLDER_SVG)

@app.route('/gallery')
def gallery():
    return redirect(url_for('index'))

# BUG FIX: 实现footer链接的实际内容页面
@app.route('/about')
def about():
    return render_template_string(ABOUT_TEMPLATE)

@app.route('/contact')
def contact():
    return render_template_string(CONTACT_TEMPLATE)

@app.route('/privacy')
def privacy():
    return render_template_string(PRIVACY_TEMPLATE)

@app.route('/terms')
def terms():
    return render_template_string(TERMS_TEMPLATE)

@app.route('/health')
def health_check():
    """简单的健康检查端点"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat()
    })

# BUG FIX: 隐私政策和使用条款模板
PRIVACY_TEMPLATE = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>隐私政策 - 春色写真馆</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }

        /* 移除所有列表标记 */
        ul, ol { list-style: none; }
        li::marker { display: none; }

        body {
            font-family: 'PingFang SC', 'Hiragino Sans GB', 'Noto Sans CJK SC', 'Source Han Sans SC', 'Microsoft YaHei', '微软雅黑', sans-serif;
            background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 25%, #f0fff4 50%, #fff8dc 75%, #fdf5e6 100%);
            min-height: 100vh;
            color: #5d4e37;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
        }
        .header {
            text-align: center;
            margin-bottom: 3rem;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
        }
        .content {
            background: rgba(255, 255, 255, 0.9);
            padding: 2rem;
            border-radius: 20px;
            margin-bottom: 2rem;
        }
        h1 { color: #8b4513; margin-bottom: 1rem; }
        h2 { color: #cd853f; margin: 2rem 0 1rem 0; }
        .highlight {
            background: rgba(255, 182, 193, 0.1);
            padding: 1rem;
            border-radius: 10px;
            border-left: 4px solid #ff69b4;
            margin: 1rem 0;
        }
        .back-link {
            display: inline-block;
            padding: 0.8rem 1.5rem;
            background: linear-gradient(45deg, #ff69b4, #ffa500);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            transition: all 0.3s ease;
        }
        .back-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 105, 180, 0.4);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔒 隐私政策</h1>
            <p>我们重视并保护您的个人隐私</p>
            <small>最后更新：2025年7月</small>
        </div>

        <div class="content">
            <div class="highlight">
                <strong>重要提示：</strong>本软件为本地使用软件，所有数据均存储在用户本地设备上。
            </div>

            <h2>1. 本地数据存储</h2>
            <p>本软件采用本地数据存储方式：</p>
            <ul>
                <li><strong>用户数据：</strong>用户名、密码等信息存储在本地数据库</li>
                <li><strong>使用记录：</strong>浏览记录、收藏等数据仅保存在本地</li>
                <li><strong>图片文件：</strong>所有图片文件存储在用户指定的本地目录</li>
            </ul>

            <h2>2. 数据安全</h2>
            <p>由于采用本地存储：</p>
            <ul>
                <li>数据不会上传到任何远程服务器</li>
                <li>用户完全控制自己的数据</li>
                <li>无网络传输风险</li>
                <li>数据安全由用户设备安全保障</li>
            </ul>

            <h2>3. 隐私保护</h2>
            <div style="background: rgba(255, 99, 71, 0.1); padding: 1rem; border-radius: 10px; border-left: 4px solid #ff6347; margin: 1rem 0;">
                <strong>⚠️ 重要说明</strong>
                <ul>
                    <li>本软件不收集任何用户隐私信息</li>
                    <li>不进行任何形式的数据上传或共享</li>
                    <li>用户需自行保护本地数据安全</li>
                </ul>
            </div>

            <h2>4. 用户责任</h2>
            <p>用户需要：</p>
            <ul>
                <li>保护好本地设备和数据安全</li>
                <li>定期备份重要数据</li>
                <li>确保软件使用符合当地法规</li>
                <li>不将软件部署到网络环境</li>
            </ul>

            <h2>5. 开发者声明</h2>
            <div class="highlight">
                <strong>开发者承诺：</strong>本软件不会收集、传输或存储任何用户隐私信息到远程服务器。
            </div>

            <h2>6. 数据控制</h2>
            <p>用户拥有完全的数据控制权：</p>
            <ul>
                <li>可随时删除本地数据</li>
                <li>可自由迁移数据文件</li>
                <li>可完全卸载软件</li>
                <li>无需担心远程数据残留</li>
            </ul>
        </div>

        <div style="text-align: center;">
            <a href="/" class="back-link">🏠 返回首页</a>
        </div>
    </div>
</body>
</html>'''

TERMS_TEMPLATE = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>使用条款 - 春色写真馆</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }

        /* 移除所有列表标记 */
        ul, ol { list-style: none; }
        li::marker { display: none; }

        body {
            font-family: 'PingFang SC', 'Hiragino Sans GB', 'Noto Sans CJK SC', 'Source Han Sans SC', 'Microsoft YaHei', '微软雅黑', sans-serif;
            background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 25%, #f0fff4 50%, #fff8dc 75%, #fdf5e6 100%);
            min-height: 100vh;
            color: #5d4e37;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
        }
        .header {
            text-align: center;
            margin-bottom: 3rem;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
        }
        .content {
            background: rgba(255, 255, 255, 0.9);
            padding: 2rem;
            border-radius: 20px;
            margin-bottom: 2rem;
        }
        h1 { color: #8b4513; margin-bottom: 1rem; }
        h2 { color: #cd853f; margin: 2rem 0 1rem 0; }
        .warning {
            background: rgba(255, 99, 71, 0.1);
            padding: 1rem;
            border-radius: 10px;
            border-left: 4px solid #ff6347;
            margin: 1rem 0;
        }
        .highlight {
            background: rgba(255, 182, 193, 0.1);
            padding: 1rem;
            border-radius: 10px;
            border-left: 4px solid #ff69b4;
            margin: 1rem 0;
        }
        .back-link {
            display: inline-block;
            padding: 0.8rem 1.5rem;
            background: linear-gradient(45deg, #ff69b4, #ffa500);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            transition: all 0.3s ease;
        }
        .back-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 105, 180, 0.4);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📋 使用条款</h1>
            <p>使用本服务前请仔细阅读以下条款</p>
            <small>最后更新：2025年7月</small>
        </div>

        <div class="content">
            <div class="warning">
                <strong>重要声明：</strong>本软件仅供本地个人使用，严禁部署到互联网或用于任何形式的网络服务。
            </div>

            <h2>1. 软件性质</h2>
            <p>春色写真馆是一个本地图片管理软件，提供：</p>
            <ul>
                <li>本地图片浏览和管理功能</li>
                <li>个人收藏管理</li>
                <li>本地数据存储</li>
                <li>离线使用体验</li>
            </ul>

            <h2>2. 使用限制</h2>
            <div class="warning">
                <strong>严格限制：</strong>
                <ul>
                    <li>仅限个人本地使用</li>
                    <li>禁止部署到任何网络服务器</li>
                    <li>禁止用于商业用途</li>
                    <li>禁止未经授权的分发或修改</li>
                    <li>禁止用于任何违法违规活动</li>
                </ul>
            </div>

            <h2>3. 用户责任</h2>
            <div class="highlight">
                <strong>用户必须：</strong>
                <ul>
                    <li>确保使用符合当地法律法规</li>
                    <li>自行承担所有使用风险</li>
                    <li>保护本地数据和设备安全</li>
                    <li>不将软件用于网络部署</li>
                    <li>尊重知识产权和他人权益</li>
                </ul>
            </div>

            <h2>4. 开发者免责</h2>
            <div class="warning">
                <strong>开发者声明：</strong>
                <ul>
                    <li>软件按"现状"提供，无任何保证</li>
                    <li>不承担任何直接或间接损失责任</li>
                    <li>不对软件误用承担法律责任</li>
                    <li>不提供任何形式的技术支持保证</li>
                    <li>保留随时停止开发的权利</li>
                </ul>
            </div>

            <h2>5. 知识产权</h2>
            <ul>
                <li>软件代码遵循开源协议</li>
                <li>用户需尊重第三方知识产权</li>
                <li>禁止商业化使用或分发</li>
                <li>修改和分发需遵循开源协议</li>
            </ul>

            <h2>6. 法律适用</h2>
            <p>用户使用本软件需遵守所在地区的法律法规。如有争议，用户自行承担法律后果。</p>

            <h2>7. 条款变更</h2>
            <p>开发者保留修改本条款的权利。继续使用软件即表示接受修改后的条款。</p>
        </div>

        <div style="text-align: center;">
            <a href="/" class="back-link">🏠 返回首页</a>
        </div>
    </div>
</body>
</html>'''

if __name__ == '__main__':
    # 确保下载目录和静态目录存在
    if not os.path.exists('downloaded'):
        os.makedirs('downloaded')
        categories = ['korea', 'cosplay', 'japan', 'gravure', 'chinese', 'thailand']
        for category in categories:
            os.makedirs(f'downloaded/{category}', exist_ok=True)
            
    if not os.path.exists('static'):
        os.makedirs('static')


    # 只进行基本的数据库初始化（创建表结构）
    init_db()
    
    # 启动应用
    print("🌸 春色写真馆启动中...")
    print("📱 访问地址: http://localhost:5000")
    print("🛠️ 管理面板: http://localhost:5000/admin (登录后可用)")
    print("💡 提示: 图库扫描和文件名修复已移至管理面板，可按需手动执行")

    app.run(debug=True, host='0.0.0.0', port=5000)