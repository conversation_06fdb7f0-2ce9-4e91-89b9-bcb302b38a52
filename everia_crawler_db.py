import asyncio
import json
import os
import re
import signal
import sys
import time
from typing import Dict, Any, <PERSON>, Tuple, Optional
from urllib.parse import urljoin, unquote

import httpx
import uvicorn
from bs4 import BeautifulSoup
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, Query
from fastapi.responses import HTMLResponse, JSONResponse
from loguru import logger
import aiosqlite
import aiofiles

try:
    import pyppeteer
    PYPPETEER_AVAILABLE = True
except ImportError:
    PYPPETEER_AVAILABLE = False


# --- 核心配置与全局变量  ---
DB_FILE = 'everia_gallery.db'
LOG_FILE = "crawler_log.txt"
ERROR_LOG_FILE = "error_log.json"
CATEGORIES: Dict[str, str] = {
    'korea': 'https://everia.club/category/korea/', 'cosplay': 'https://everia.club/category/cosplay/',
    'japan': 'https://everia.club/category/japan/', 'gravure': 'https://everia.club/category/gravure/',
    'chinese': 'https://everia.club/category/chinese/','thailand': 'https://everia.club/category/thailand/',
}
HTTP_TIMEOUT = 30

MAX_CONCURRENT_ALBUMS = 100  # 同时处理的图集任务数 (激进值)
PAGE_SCAN_CONCURRENCY = 50   # 同时扫描的分类页面任务数 (激进值)

# Pyppeteer 相关配置
PYPPETEER_PAGE_TIMEOUT = 20000
PYPPETEER_WAIT_DELAY = 3000

# --- 异步状态管理与通信 ---
CRAWL_RUNNING = asyncio.Event()
FORCE_STOP = asyncio.Event()  # 强制停止标志
broadcast_queue = asyncio.Queue()
active_tasks = set()  # 跟踪活跃任务
shared_state: Dict[str, Any] = {
    "stats": {"totalAlbums": 0, "totalImages": 0},
    "status": "空闲", "is_crawling": False,
    "current_category": "",
    "lock": asyncio.Lock(),
    "browser": None
}
error_log_lock = asyncio.Lock()

# 信号处理器
def signal_handler(signum, frame):
    """处理 Ctrl+C 等信号"""
    logger.warning(f"收到信号 {signum}，正在强制停止所有任务...")
    FORCE_STOP.set()
    CRAWL_RUNNING.clear()

    # 取消所有活跃任务
    for task in active_tasks.copy():
        if not task.done():
            task.cancel()

    # 给一些时间让任务清理
    import time
    time.sleep(2)

    logger.info("强制退出程序")
    sys.exit(0)

# 注册信号处理器
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

# --- 日志配置 ---
logger.remove()
logger.add(sys.stdout, level="INFO", format="<green>{time:HH:mm:ss}</green>| <level>{level: <7}</level>| <level></level>")
logger.add(LOG_FILE, encoding='utf-8', level="DEBUG", rotation="10 MB")

async def log_to_ui(message: str, m_type: str = "info"):
    color_map = {"info": "#e0e0e0", "success": "#4caf50", "error": "#f44336", "warning": "#ff9800", "primary": "#00bcd4"}
    html_message = f'<span style="color:{color_map.get(m_type, "#e0e0e0")};">{time.strftime("%H:%M:%S")} | {message}</span>'
    plain_message = f"{time.strftime('%H:%M:%S')} | {re.sub('<[^<]+?>', '', message)}"
    await broadcast_queue.put({"type": "log", "html": html_message, "plain": plain_message})

# --- WebSocket 管理器 ---
class WebSocketManager:
    def __init__(self): self.active_connections: list[WebSocket] = []
    async def connect(self, ws: WebSocket): await ws.accept(); self.active_connections.append(ws)
    def disconnect(self, ws: WebSocket): self.active_connections.remove(ws)
    async def broadcast(self, data: dict):
        if self.active_connections:
            await asyncio.gather(*[c.send_json(data) for c in self.active_connections], return_exceptions=True)

ws_manager = WebSocketManager()
app = FastAPI()

# --- 前端代码 [爬虫控制台] ---
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>春色写真馆 - 爬虫控制台 V18.1</title>
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIGZpbGw9IiNGRkI2QzEiIHJ4PSI2Ii8+CiAgPGNpcmNsZSBjeD0iMTYiIGN5PSIxMiIgcj0iMyIgZmlsbD0iI0ZGNjlCNCIvPgogIDxjaXJjbGUgY3g9IjEyIiBjeT0iMTYiIHI9IjIuNSIgZmlsbD0iI0ZGMTQ5MyIvPgogIDxjaXJjbGUgY3g9IjIwIiBjeT0iMTYiIHI9IjIuNSIgZmlsbD0iI0ZGMTQ5MyIvPgogIDxjaXJjbGUgY3g9IjE2IiBjeT0iMjAiIHI9IjIiIGZpbGw9IiNEQzE0M0MiLz4KICA8Y2lyY2xlIGN4PSIxMCIgY3k9IjIwIiByPSIxLjUiIGZpbGw9IiNGRjY5QjQiLz4KICA8Y2lyY2xlIGN4PSIyMiIgY3k9IjIwIiByPSIxLjUiIGZpbGw9IiNGRjY5QjQiLz4KPC9zdmc+">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }

        /* 移除所有列表标记 */
        ul, ol { list-style: none; }
        li::marker { display: none; }

        body {
            font-family: 'PingFang SC', 'Hiragino Sans GB', 'Noto Sans CJK SC', 'Source Han Sans SC', 'Microsoft YaHei', '微软雅黑', sans-serif;
            background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 25%, #f0fff4 50%, #fff8dc 75%, #fdf5e6 100%);
            min-height: 100vh;
            padding: 20px;
            color: #5d4e37;
            line-height: 1.6;
            font-size: 16px;
        }
        .container{max-width:1200px;margin:20px auto;padding:0 20px}
        #loader{position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(255, 238, 248, 0.95);z-index:9999;display:flex;justify-content:center;align-items:center;flex-direction:column;transition:opacity .5s}
        #loader.hidden{opacity:0;pointer-events:none}
        .spinner{width:50px;height:50px;border:5px solid rgba(255, 182, 193, 0.3);border-top-color:#ff69b4;border-radius:50%;animation:spin 1s linear infinite}
        #loader-status{margin-top:20px;font-size:1.2em;color:#8b4513}
        @keyframes spin{to{transform:rotate(360deg)}}
        .grid{display:grid;grid-template-columns:repeat(2, 1fr);gap:20px;align-items:start}
        .card{background:rgba(255, 255, 255, 0.9);backdrop-filter:blur(10px);border-radius:20px;padding:30px;box-shadow:0 4px 15px rgba(255, 182, 193, 0.2);border:1px solid rgba(255, 182, 193, 0.1)}
        h1,h2{color:#8b4513;margin-bottom:15px;text-shadow:2px 2px 4px rgba(255, 182, 193, 0.3)}
        button, .db-button{background:linear-gradient(135deg, #ff69b4 0%, #ff1493 100%);color:#fff;border:none;padding:12px 20px;border-radius:10px;cursor:pointer;font-size:1em;transition:all .3s ease;margin-right:10px;position:relative;text-decoration:none;display:inline-block;box-shadow:0 2px 10px rgba(255, 105, 180, 0.3)}
        button:hover:not(:disabled), .db-button:hover{transform:translateY(-2px);box-shadow:0 5px 15px rgba(255, 105, 180, 0.4)}
        button:disabled{background:rgba(255, 182, 193, 0.5);cursor:not-allowed;transform:none;box-shadow:none}
        .db-button { background:linear-gradient(135deg, #4caf50 0%, #45a049 100%); }
        button .btn-text.hidden{visibility:hidden}
        button .btn-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);display:none;width:20px;height:20px;border:2px solid #fff;border-top-color:transparent;border-radius:50%;animation:spin .6s linear infinite}
        button.loading .btn-loader{display:block}
        .log-container{height:500px;overflow-y:scroll;background:rgba(255, 240, 245, 0.7);padding:15px;border-radius:10px;font-family:monospace;font-size:.9em;white-space:pre-wrap;margin-top:20px;border:1px solid rgba(255, 182, 193, 0.3)}
        .stats-grid{display:grid;grid-template-columns:repeat(auto-fit,minmax(150px,1fr));gap:15px}
        .stat-item{background:rgba(255, 240, 245, 0.7);padding:15px;border-radius:10px;text-align:center;border:1px solid rgba(255, 182, 193, 0.3)}
        .stat-item .label{font-size:.9em;opacity:.7;color:#5d4e37}.stat-item .value{font-size:1.5em;font-weight:700;color:#8b4513}
        .category-checklist{display:grid;grid-template-columns:repeat(auto-fill,minmax(150px,1fr));gap:10px;margin-bottom:20px}
        .category-checklist label{background:rgba(255, 240, 245, 0.7);padding:8px 12px;border-radius:10px;cursor:pointer;display:flex;align-items:center;border:1px solid rgba(255, 182, 193, 0.3);transition:all .3s ease}
        .category-checklist label:hover{background:rgba(255, 182, 193, 0.3)}
        .category-checklist input{margin-right:10px}
    </style>
</head>
<body>
<div id="loader"><div class="spinner"></div><p id="loader-status">正在连接服务器...</p></div>
<div id="app" class="container">
    <h1>🌸 春色写真馆 - 爬虫控制台 <span style="font-size: 0.5em; color: #ff69b4;">V18.1</span></h1>
    <div class="grid">
        <div>
            <div class="card">
                <h2>控制与设置</h2>
                <div id="category-checklist-container">
                    <h3>选择要爬取的分类</h3>
                    <div id="category-checklist" class="category-checklist"></div>
                </div>
                <div style="margin-top:20px;">
                    <button id="start-crawl-btn"><span class="btn-text">开始采集</span><span class="btn-loader"></span></button>
                    <button id="stop-crawl-btn" disabled><span class="btn-text">停止采集</span><span class="btn-loader"></span></button>
                    <a href="/db_viewer" target="_blank" class="db-button">查看数据库</a>
                </div>
            </div>
            <div class="card" style="margin-top: 20px;">
                <h2>实时统计</h2>
                <div id="stats-grid" class="stats-grid">
                    <div class="stat-item"><div class="label">任务状态</div><div class="value" id="stat-status">空闲</div></div>
                    <div class="stat-item"><div class="label">当前分类</div><div class="value" id="stat-current-category">N/A</div></div>
                    <div class="stat-item"><div class="label">已存图集</div><div class="value" id="stat-total-albums">0</div></div>
                    <div class="stat-item"><div class="label">已存图片链接</div><div class="value" id="stat-total-images">0</div></div>
                </div>
            </div>
        </div>
        <div>
            <div class="card">
                <h2>UI 日志</h2>
                <div id="log-container" class="log-container"></div>
            </div>
        </div>
    </div>
</div>
<script>
(() => {
    const $ = (s) => document.querySelector(s);
    let ws;
    const dom = {
        loader: $('#loader'), loaderStatus: $('#loader-status'),
        logContainer: $('#log-container'),
        startBtn: $('#start-crawl-btn'), stopBtn: $('#stop-crawl-btn'),
        categoryChecklist: $('#category-checklist'),
        stats: {
            status: $('#stat-status'), currentCategory: $('#stat-current-category'),
            totalAlbums: $('#stat-total-albums'), totalImages: $('#stat-total-images')
        }
    };
    let state = { isCrawling: false };

    const setButtonLoading = (btn, isLoading) => {
        if (!btn) return;
        btn.classList.toggle('loading', isLoading);
        if (btn === dom.stopBtn) {
            btn.disabled = isLoading || !state.isCrawling;
        } else {
            btn.disabled = state.isCrawling || isLoading;
        }
        btn.querySelector('.btn-text').classList.toggle('hidden', isLoading);
    };
    const addLog = (html, plainText) => {
        dom.logContainer.insertAdjacentHTML('beforeend', `<p>${html}</p>`);
        if (dom.logContainer.children.length > 500) dom.logContainer.firstElementChild.remove();
        dom.logContainer.scrollTop = dom.logContainer.scrollHeight;
        console.log(plainText);
    };
    const updateStats = (s) => {
        dom.stats.totalAlbums.textContent = s.totalAlbums;
        dom.stats.totalImages.textContent = s.totalImages;
    };
    const setAppBusyState = (isCrawling) => {
        state.isCrawling = isCrawling;
        dom.startBtn.disabled = isCrawling;
        dom.stopBtn.disabled = !isCrawling;
        // setButtonLoading(dom.startBtn, isCrawling); // This line can cause visual bugs, let's simplify
    };
    const updateStatusText = (status = '空闲', currentCategory = 'N/A') => {
        dom.stats.status.textContent = status;
        dom.stats.currentCategory.textContent = currentCategory || 'N/A';
    };

    const connectWebSocket = () => {
        ws = new WebSocket(`${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/ws`);
        ws.onopen = () => { dom.loaderStatus.textContent = '连接成功，请求初始状态...'; ws.send(JSON.stringify({ action: 'get_initial_state' })); };
        ws.onclose = () => { dom.loader.classList.remove('hidden'); dom.loaderStatus.textContent = '与服务器断开连接, 3秒后重连...'; setTimeout(connectWebSocket, 3000) };
        ws.onmessage = (event) => {
            const data = JSON.parse(event.data);
            switch (data.type) {
                case 'full_state':
                    dom.loader.classList.add('hidden');
                    updateStatusText(data.status, data.current_category);
                    setAppBusyState(data.is_crawling);
                    updateStats(data.stats);
                    break;
                case 'log': addLog(data.html, data.plain); break;
                case 'state_update':
                    if (data.status !== undefined) updateStatusText(data.status, data.current_category);
                    if (data.is_crawling !== undefined) setAppBusyState(data.is_crawling);
                    if (data.stats) updateStats(data.stats);
                    break;
                case 'categories_list':
                    dom.categoryChecklist.innerHTML = '';
                    data.categories.forEach(c => { const id = `cat-${c}`; dom.categoryChecklist.innerHTML += `<label for="${id}"><input type="checkbox" id="${id}" value="${c}"> ${c}</label>`; });
                    break;
            }
        };
    };

    const setupEventListeners = () => {
        dom.startBtn.addEventListener('click', () => {
            const selected = Array.from(document.querySelectorAll('#category-checklist input:checked')).map(cb => cb.value);
            if (selected.length === 0) return addLog('<span style="color:var(--warning-color)">请至少选择一个分类！</span>', '请至少选择一个分类！');
            ws.send(JSON.stringify({ action: 'start_crawl', categories: selected }));
        });
        dom.stopBtn.addEventListener('click', () => { ws.send(JSON.stringify({ action: 'stop_crawl' })) });
    };

    document.addEventListener('DOMContentLoaded', () => { setupEventListeners(); connectWebSocket(); });
})();
</script>
</body>
</html>
"""

# 数据库查看器前端代码 ---
DB_VIEWER_HTML = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>春色写真馆 - 数据库查看器</title>
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIGZpbGw9IiNGRkI2QzEiIHJ4PSI2Ii8+CiAgPGNpcmNsZSBjeD0iMTYiIGN5PSIxMiIgcj0iMyIgZmlsbD0iI0ZGNjlCNCIvPgogIDxjaXJjbGUgY3g9IjEyIiBjeT0iMTYiIHI9IjIuNSIgZmlsbD0iI0ZGMTQ5MyIvPgogIDxjaXJjbGUgY3g9IjIwIiBjeT0iMTYiIHI9IjIuNSIgZmlsbD0iI0ZGMTQ5MyIvPgogIDxjaXJjbGUgY3g9IjE2IiBjeT0iMjAiIHI9IjIiIGZpbGw9IiNEQzE0M0MiLz4KICA8Y2lyY2xlIGN4PSIxMCIgY3k9IjIwIiByPSIxLjUiIGZpbGw9IiNGRjY5QjQiLz4KICA8Y2lyY2xlIGN4PSIyMiIgY3k9IjIwIiByPSIxLjUiIGZpbGw9IiNGRjY5QjQiLz4KPC9zdmc+">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }

        /* 移除所有列表标记 */
        ul, ol { list-style: none; }
        li::marker { display: none; }

        body {
            font-family: 'PingFang SC', 'Hiragino Sans GB', 'Noto Sans CJK SC', 'Source Han Sans SC', 'Microsoft YaHei', '微软雅黑', sans-serif;
            background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 25%, #f0fff4 50%, #fff8dc 75%, #fdf5e6 100%);
            min-height: 100vh;
            padding: 20px;
            color: #5d4e37;
        }
        .container { max-width: 1400px; margin: 0 auto; }
        h1, h2 { color: #8b4513; border-bottom: 2px solid rgba(255, 182, 193, 0.3); padding-bottom: 10px; text-shadow: 2px 2px 4px rgba(255, 182, 193, 0.3); }
        .stats { display: flex; gap: 20px; background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px); padding: 20px; border-radius: 15px; margin-bottom: 20px; box-shadow: 0 4px 15px rgba(255, 182, 193, 0.2); border: 1px solid rgba(255, 182, 193, 0.1); }
        .stat-item { text-align: center; }
        .stat-item .label { font-size: 0.9em; opacity: 0.7; color: #5d4e37; }
        .stat-item .value { font-size: 1.8em; font-weight: bold; color: #8b4513; }
        table { width: 100%; border-collapse: collapse; background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px); border-radius: 15px; overflow: hidden; box-shadow: 0 4px 15px rgba(255, 182, 193, 0.2); }
        th, td { padding: 12px 15px; border: 1px solid rgba(255, 182, 193, 0.2); text-align: left; }
        th { background: rgba(255, 182, 193, 0.3); font-weight: bold; color: #8b4513; }
        tr:nth-child(even) { background: rgba(255, 240, 245, 0.5); }
        tr:hover { background: rgba(255, 182, 193, 0.2); }
        .album-title { cursor: pointer; color: #ff69b4; font-weight: 500; }
        .album-title:hover { text-decoration: underline; }
        a { color: #ff1493; text-decoration: none; }
        a:hover { text-decoration: underline; }
        #loader { text-align: center; padding: 20px; font-size: 1.2em; display: none; color: #8b4513; }
        dialog { background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); color: #5d4e37; border: 1px solid rgba(255, 182, 193, 0.3); border-radius: 15px; max-width: 80vw; width: 900px; padding: 0; box-shadow: 0 10px 30px rgba(255, 182, 193, 0.3);}
        dialog::backdrop { background: rgba(255, 182, 193, 0.3); }
        .dialog-header { padding: 15px 20px; background: rgba(255, 182, 193, 0.2); display: flex; justify-content: space-between; align-items: center; border-radius: 15px 15px 0 0; }
        .dialog-header h3 { margin: 0; color: #8b4513; }
        .dialog-header button { background: none; border: none; font-size: 1.5em; color: #8b4513; cursor: pointer; }
        .dialog-body { padding: 20px; max-height: 70vh; overflow-y: auto; }
        .dialog-body ul { list-style-type: none; padding: 0; }
        .dialog-body li { padding: 5px 0; border-bottom: 1px solid rgba(255, 182, 193, 0.2); word-break: break-all; color: #5d4e37; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌸 春色写真馆 - 数据库查看器</h1>
        <div class="stats">
            <div class="stat-item">
                <div class="label">总图集数 (Albums)</div>
                <div class="value" id="total-albums">0</div>
            </div>
            <div class="stat-item">
                <div class="label">总图片链接数 (Images)</div>
                <div class="value" id="total-images">0</div>
            </div>
        </div>
        <h2>图集列表 (Albums)</h2>
        <table>
            <thead>
                <tr>
                    <th>ID</th>
                    <th>标题 (点击查看图片链接)</th>
                    <th>分类</th>
                    <th>图片数</th>
                    <th>来源URL</th>
                    <th>入库时间</th>
                </tr>
            </thead>
            <tbody id="albums-tbody"></tbody>
        </table>
        <div id="loader">正在加载更多...</div>
    </div>

    <dialog id="image-dialog">
        <div class="dialog-header">
            <h3 id="dialog-title">图集图片</h3>
            <button id="dialog-close-btn">&times;</button>
        </div>
        <div class="dialog-body">
            <ul id="image-list"></ul>
        </div>
    </dialog>

    <script>
    (() => {
        const $ = s => document.querySelector(s);
        const albumsTbody = $('#albums-tbody');
        const loader = $('#loader');
        const imageDialog = $('#image-dialog');
        const imageList = $('#image-list');
        const dialogTitle = $('#dialog-title');
        
        let offset = 0;
        const limit = 50; // 每次加载50条
        let isLoading = false;
        let hasMore = true;

        const fetchAlbums = async () => {
            if (isLoading || !hasMore) return;
            isLoading = true;
            loader.style.display = 'block';

            try {
                const response = await fetch(`/api/albums?offset=${offset}&limit=${limit}`);
                const data = await response.json();
                
                $('#total-albums').textContent = data.total_albums;
                $('#total-images').textContent = data.total_images;

                data.albums.forEach(album => {
                    const tr = document.createElement('tr');
                    tr.innerHTML = `
                        <td>${album.id}</td>
                        <td><span class="album-title" data-id="${album.id}" data-title="${album.title}">${album.title}</span></td>
                        <td>${album.category}</td>
                        <td>${album.image_count}</td>
                        <td><a href="${album.url}" target="_blank">源链接</a></td>
                        <td>${new Date(album.created_at).toLocaleString()}</td>
                    `;
                    albumsTbody.appendChild(tr);
                });

                offset += data.albums.length;
                if (offset >= data.total_albums) {
                    hasMore = false;
                    loader.textContent = '已加载全部数据';
                }
            } catch (error) {
                console.error('获取图集失败:', error);
                loader.textContent = '加载失败，请刷新重试';
            } finally {
                isLoading = false;
                if(hasMore) loader.style.display = 'none';
            }
        };

        const fetchAndShowImages = async (albumId, albumTitle) => {
            dialogTitle.textContent = `图集: ${albumTitle}`;
            imageList.innerHTML = '<li>正在加载...</li>';
            imageDialog.showModal();

            try {
                const response = await fetch(`/api/album/${albumId}/images`);
                const images = await response.json();
                imageList.innerHTML = images.length > 0
                    ? images.map(img => `<li>${img.url}</li>`).join('')
                    : '<li>此图集下未找到图片链接。</li>';
            } catch (error) {
                console.error('获取图片链接失败:', error);
                imageList.innerHTML = '<li>加载图片链接失败。</li>';
            }
        };

        // 无限滚动监听
        const observer = new IntersectionObserver(entries => {
            if (entries[0].isIntersecting) {
                fetchAlbums();
            }
        }, { threshold: 1.0 });

        observer.observe(loader);
        
        // 事件委托：点击标题打开弹窗
        albumsTbody.addEventListener('click', e => {
            if (e.target.classList.contains('album-title')) {
                const albumId = e.target.dataset.id;
                const albumTitle = e.target.dataset.title;
                fetchAndShowImages(albumId, albumTitle);
            }
        });

        // 关闭弹窗
        $('#dialog-close-btn').addEventListener('click', () => imageDialog.close());
        imageDialog.addEventListener('click', (event) => {
             if (event.target === imageDialog) {
                imageDialog.close();
             }
        });

        // 初始加载
        fetchAlbums();
    })();
    </script>
</body>
</html>
"""

def clean_filename(text: str) -> str:
    return re.sub(r'[\\/*?:"<>|]', '_', text).strip()

# --- 数据库和处理逻辑 ---

async def init_db():
    """初始化数据库，创建所需的表结构。"""
    async with aiosqlite.connect(DB_FILE) as db:
        await db.execute("PRAGMA journal_mode=WAL;")  # 开启WAL模式，提高并发写入性能
        await db.execute("""
            CREATE TABLE IF NOT EXISTS processed_urls (
                url TEXT PRIMARY KEY,
                album_title TEXT NOT NULL,
                processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        await db.execute("""
            CREATE TABLE IF NOT EXISTS albums (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                url TEXT NOT NULL UNIQUE,
                category TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        await db.execute("""
            CREATE TABLE IF NOT EXISTS images (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                album_id INTEGER NOT NULL,
                url TEXT NOT NULL UNIQUE,
                FOREIGN KEY (album_id) REFERENCES albums (id)
            )
        """)
        await db.commit()
    logger.info("数据库初始化完成，已开启WAL模式。")

async def get_initial_stats_from_db() -> Dict[str, int]:
    """从数据库获取初始统计数据。"""
    stats = {'totalAlbums': 0, 'totalImages': 0}
    if not os.path.exists(DB_FILE):
        return stats
    try:
        async with aiosqlite.connect(DB_FILE) as db:
            async with db.execute("SELECT COUNT(*) FROM albums") as cursor:
                stats['totalAlbums'] = (await cursor.fetchone())[0]
            async with db.execute("SELECT COUNT(*) FROM images") as cursor:
                stats['totalImages'] = (await cursor.fetchone())[0]
    except Exception as e:
        logger.error(f"从数据库加载统计数据失败: {e}")
    return stats

async def log_failed_album(url: str, title: str, reason: str):
    async with error_log_lock:
        error_entry = {"url": url, "title": title, "reason": reason, "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")}
        try:
            errors = []
            if os.path.exists(ERROR_LOG_FILE):
                with open(ERROR_LOG_FILE, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if content: errors = json.loads(content)
            if not any(e['url'] == url for e in errors):
                errors.append(error_entry)
                with open(ERROR_LOG_FILE, 'w', encoding='utf-8') as f:
                    json.dump(errors, f, indent=4, ensure_ascii=False)
        except Exception as e:
            logger.error(f"写入错误日志 '{ERROR_LOG_FILE}' 失败: {e}")

async def update_shared_state(data_to_update: Dict):
    async with shared_state['lock']:
        for key, value in data_to_update.items():
            if isinstance(shared_state.get(key), dict) and isinstance(value, dict):
                shared_state[key].update(value)
            else:
                shared_state[key] = value
        state_copy = {k: v for k, v in shared_state.items() if k not in ['lock', 'browser']}
        await broadcast_queue.put({"type": "state_update", **state_copy})

def parse_page_for_links(html_content: str, base_url: str) -> Tuple[List[Dict[str, str]], Optional[int]]:
    soup = BeautifulSoup(html_content, 'lxml')
    links, max_page = [], None
    for a in soup.select('a.thumbnail-link'):
        img = a.find('img')
        if img and not (a.find_parent('div', class_='post_thumb post_thumb_top') or (img.get('width') == '360' and img.get('height') == '540')):
            title = img.get('title') or unquote(a['href'].strip('/').split('/')[-1]).replace('-', ' ').title()
            links.append({'url': urljoin(base_url, a['href']), 'title': title})
    page_numbers = [int(p.text) for p in soup.select('a.page-numbers') if p.text.isdigit()]
    if page_numbers: max_page = max(page_numbers)
    return links, max_page

def parse_album_for_images(html_content: str) -> List[str]:
    soup = BeautifulSoup(html_content, 'lxml')
    return sorted(list({src for img in soup.select('div.separator img, figure.wp-block-image img') if (src := img.get('src') or img.get('data-src'))}))

async def get_html_with_pyppeteer(url: str) -> str:
    if not shared_state.get("browser"): return ""
    page = None
    try:
        page = await shared_state["browser"].newPage()
        await page.goto(url, timeout=PYPPETEER_PAGE_TIMEOUT)
        await asyncio.sleep(PYPPETEER_WAIT_DELAY)
        return await page.content()
    except Exception as e:
        logger.error(f"Pyppeteer获取页面 {url} 失败: {e}")
        return ""
    finally:
        if page: await page.close()

async def add_url_to_processed(db: aiosqlite.Connection, url: str, album_title: str):
    await db.execute("INSERT OR IGNORE INTO processed_urls (url, album_title) VALUES (?, ?)", (url, album_title))

async def process_album_and_store_in_db(db: aiosqlite.Connection, page_info: Dict, category: str, client: httpx.AsyncClient) -> bool:
    # 检查停止标志
    if not CRAWL_RUNNING.is_set() or FORCE_STOP.is_set():
        return False

    album_url = page_info['url']
    album_name = clean_filename(page_info['title'])
    try:
        # 在网络请求前再次检查停止标志
        if not CRAWL_RUNNING.is_set() or FORCE_STOP.is_set():
            return False

        resp = await client.get(album_url, timeout=HTTP_TIMEOUT, follow_redirects=True)
        resp.raise_for_status()
        html_content = resp.text

        # 检查停止标志
        if not CRAWL_RUNNING.is_set() or FORCE_STOP.is_set():
            return False

        soup = BeautifulSoup(html_content, 'lxml')
        if header_tag := soup.select_one('h1.entry-title, h2.single-post-title'):
            if header_text := header_tag.text.strip():
                album_name = clean_filename(header_text)

        image_urls = parse_album_for_images(html_content)
        if not image_urls and PYPPETEER_AVAILABLE and CRAWL_RUNNING.is_set() and not FORCE_STOP.is_set():
            await log_to_ui(f"图集 [<b>{album_name}</b>] 初始解析无果, 启动浏览器模式重试...", m_type="warning")
            if html_content := await get_html_with_pyppeteer(album_url):
                image_urls = parse_album_for_images(html_content)

        if not image_urls:
            await log_to_ui(f"图集 [<b>{album_name}</b>] 未能解析到任何图片链接，跳过。", m_type="error")
            await log_failed_album(album_url, album_name, "解析图片链接失败")
            return False

        # 检查停止标志
        if not CRAWL_RUNNING.is_set() or FORCE_STOP.is_set():
            return False

        cursor = await db.execute("INSERT OR IGNORE INTO albums (title, url, category) VALUES (?, ?, ?)", (album_name, album_url, category))
        album_id = cursor.lastrowid
        if album_id == 0: # 可能是已存在记录
            async with db.execute("SELECT id FROM albums WHERE url = ?", (album_url,)) as find_cursor:
                if result := await find_cursor.fetchone():
                    album_id = result[0]

        if not album_id:
            logger.error(f"无法为图集 '{album_name}' 获取或创建ID，跳过图片存储。")
            return False

        images_to_insert = [(album_id, url) for url in image_urls]
        await db.executemany("INSERT OR IGNORE INTO images (album_id, url) VALUES (?, ?)", images_to_insert)

        await add_url_to_processed(db, album_url, album_name)
        await db.commit()

        await log_to_ui(f"采集成功: [<b>{album_name}</b>] ({len(image_urls)}图)", m_type="success")
        return True
    except asyncio.CancelledError:
        logger.info(f"图集 '{album_name}' 处理被取消")
        return False
    except Exception as e:
        logger.error(f"处理图集 '{album_name}' ({album_url}) 失败: {e}")
        await log_failed_album(album_url, album_name, f"处理异常: {e}")
        return False

# --- 主任务与工作流 ---
async def album_processor_worker(db: aiosqlite.Connection, queue: asyncio.Queue, cat_name: str, client: httpx.AsyncClient):
    try:
        while CRAWL_RUNNING.is_set() and not FORCE_STOP.is_set():
            try:
                # 使用超时等待，以便能够响应停止信号
                page_info = await asyncio.wait_for(queue.get(), timeout=1.0)
                if page_info is None:
                    break

                if await process_album_and_store_in_db(db, page_info, cat_name, client):
                    async with shared_state['lock']:
                        stats_now = await get_initial_stats_from_db()
                        shared_state['stats']['totalAlbums'] = stats_now['totalAlbums']
                        shared_state['stats']['totalImages'] = stats_now['totalImages']
                    await broadcast_queue.put({"type": "state_update", "stats": stats_now})
                queue.task_done()
            except asyncio.TimeoutError:
                # 超时是正常的，继续循环检查停止标志
                continue
            except asyncio.CancelledError:
                logger.info(f"Worker for {cat_name} 被取消")
                break
    except Exception as e:
        logger.error(f"Worker for {cat_name} 异常退出: {e}")
    finally:
        # 确保队列任务被标记为完成
        try:
            while not queue.empty():
                queue.task_done()
        except ValueError:
            pass

async def scan_category_page(url: str, client: httpx.AsyncClient, processed_urls: set, album_queue: asyncio.Queue, page_num: int, cat_name: str):
    try:
        # 检查停止标志
        if not CRAWL_RUNNING.is_set() or FORCE_STOP.is_set():
            return

        resp = await client.get(url, follow_redirects=True)
        resp.raise_for_status()

        # 再次检查停止标志
        if not CRAWL_RUNNING.is_set() or FORCE_STOP.is_set():
            return

        pages, _ = parse_page_for_links(resp.text, url)
        new_pages = [p for p in pages if p['url'] not in processed_urls]
        if new_pages:
            await log_to_ui(f"扫描 <b>{cat_name}</b> P{page_num}...发现 {len(new_pages)} 个新图集。", m_type="info")
            for page in new_pages:
                if not CRAWL_RUNNING.is_set() or FORCE_STOP.is_set():
                    break
                await album_queue.put(page)
    except asyncio.CancelledError:
        logger.info(f"扫描页面 {url} 被取消")
    except Exception as e:
        logger.error(f"扫描页面 {url} 失败: {e}")

async def crawl_task(categories_to_crawl: List[str]):
    task = asyncio.current_task()
    active_tasks.add(task)

    try:
        CRAWL_RUNNING.set()
        FORCE_STOP.clear()  # 重置强制停止标志
        await update_shared_state({"status": "运行中", "is_crawling": True})
        await log_to_ui("采集任务启动...", m_type="success")

        browser_instance = None
        if PYPPETEER_AVAILABLE and not shared_state.get("browser"):
            try:
                await log_to_ui("正在启动浏览器实例...", m_type="primary")
                browser_instance = await pyppeteer.launch(headless=True, args=['--no-sandbox'])
                shared_state["browser"] = browser_instance
            except Exception as e:
                logger.error(f"启动Pyppeteer失败: {e}")
                await log_to_ui("启动浏览器失败，动态解析功能不可用。", m_type="error")

        headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'}

        try:
            async with httpx.AsyncClient(http2=True, verify=False, headers=headers, timeout=HTTP_TIMEOUT) as client, \
                       aiosqlite.connect(DB_FILE) as db:

                async with db.execute("SELECT url FROM processed_urls") as cursor:
                    processed_urls = {row[0] for row in await cursor.fetchall()}

                album_queue = asyncio.Queue(maxsize=MAX_CONCURRENT_ALBUMS * 2)
                processor_tasks = []

                for cat_name in categories_to_crawl:
                    if not CRAWL_RUNNING.is_set() or FORCE_STOP.is_set():
                        break
                    base_url = CATEGORIES.get(cat_name)
                    if not base_url:
                        continue

                    await update_shared_state({"current_category": cat_name})
                    await log_to_ui(f"--- 开始处理分类: <b>{cat_name}</b> ---", m_type="primary")

                    # 创建处理器任务
                    processor_tasks = []
                    for _ in range(MAX_CONCURRENT_ALBUMS):
                        task = asyncio.create_task(album_processor_worker(db, album_queue, cat_name, client))
                        processor_tasks.append(task)
                        active_tasks.add(task)

                    try:
                        if not CRAWL_RUNNING.is_set() or FORCE_STOP.is_set():
                            break

                        resp = await client.get(base_url, follow_redirects=True)
                        resp.raise_for_status()
                        pages, max_pages = parse_page_for_links(resp.text, base_url)

                        new_pages = [p for p in pages if p['url'] not in processed_urls]
                        for page in new_pages:
                            if not CRAWL_RUNNING.is_set() or FORCE_STOP.is_set():
                                break
                            await album_queue.put(page)

                        if max_pages and CRAWL_RUNNING.is_set() and not FORCE_STOP.is_set():
                            await log_to_ui(f"<b>{cat_name}</b>: 检测到 {max_pages} 页, 开始并发扫描...", m_type="info")
                            semaphore = asyncio.Semaphore(PAGE_SCAN_CONCURRENCY)

                            async def throttled_scan(page_num):
                                async with semaphore:
                                    if CRAWL_RUNNING.is_set() and not FORCE_STOP.is_set():
                                        await scan_category_page(f"{base_url}page/{page_num}/", client, processed_urls, album_queue, page_num, cat_name)

                            scan_tasks = []
                            for p_num in range(2, max_pages + 1):
                                if not CRAWL_RUNNING.is_set() or FORCE_STOP.is_set():
                                    break
                                task = asyncio.create_task(throttled_scan(p_num))
                                scan_tasks.append(task)
                                active_tasks.add(task)

                            # 等待扫描任务完成，但要能响应停止信号
                            if scan_tasks:
                                try:
                                    await asyncio.gather(*scan_tasks)
                                except asyncio.CancelledError:
                                    logger.info("扫描任务被取消")
                                finally:
                                    # 清理扫描任务
                                    for task in scan_tasks:
                                        active_tasks.discard(task)
                                        if not task.done():
                                            task.cancel()

                    except Exception as e:
                        logger.error(f"扫描分类 {cat_name} 首页失败: {e}")

                    # 等待队列处理完成或停止信号
                    try:
                        while not album_queue.empty() and CRAWL_RUNNING.is_set() and not FORCE_STOP.is_set():
                            await asyncio.sleep(0.1)
                    except asyncio.CancelledError:
                        pass

                    # 发送停止信号给处理器
                    for _ in processor_tasks:
                        await album_queue.put(None)

                    # 等待处理器任务完成
                    try:
                        await asyncio.gather(*processor_tasks, return_exceptions=True)
                    except Exception as e:
                        logger.error(f"等待处理器任务完成时出错: {e}")
                    finally:
                        # 清理处理器任务
                        for task in processor_tasks:
                            active_tasks.discard(task)
                            if not task.done():
                                task.cancel()

        except Exception as e:
            logger.error(f"爬取任务异常: {e}")
        finally:
            # 清理浏览器实例
            if browser_instance:
                try:
                    await log_to_ui("正在关闭浏览器实例...", m_type="primary")
                    await browser_instance.close()
                except Exception as e:
                    logger.error(f"关闭浏览器失败: {e}")
                finally:
                    shared_state["browser"] = None

        if FORCE_STOP.is_set():
            await log_to_ui("采集任务被强制停止!", m_type="warning")
        else:
            await log_to_ui("所有采集任务完成!", m_type="success")

    except asyncio.CancelledError:
        await log_to_ui("采集任务被取消!", m_type="warning")
    except Exception as e:
        logger.error(f"爬取任务异常: {e}")
        await log_to_ui(f"采集任务异常: {e}", m_type="error")
    finally:
        await update_shared_state({"status": "空闲", "is_crawling": False, "current_category": ""})
        CRAWL_RUNNING.clear()
        active_tasks.discard(task)

async def broadcaster():
    while True:
        try:
            msg = await asyncio.wait_for(broadcast_queue.get(), timeout=0.2)
            await ws_manager.broadcast(msg)
            broadcast_queue.task_done()
        except asyncio.TimeoutError: pass

# --- FastAPI 路由和事件 ---

@app.on_event("startup")
async def startup_tasks():
    await init_db()
    asyncio.create_task(broadcaster())
    if not PYPPETEER_AVAILABLE:
        logger.warning("Pyppeteer未安装, 动态内容解析功能将不可用。请运行 'pip install pyppeteer'。")

@app.get("/", response_class=HTMLResponse)
async def get_root(): return HTML_TEMPLATE

async def get_current_app_state() -> Dict:
    async with shared_state['lock']:
        return {k: v for k, v in shared_state.items() if k not in ['lock', 'browser']}

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await ws_manager.connect(websocket)
    try:
        while True:
            data = await websocket.receive_json()
            action = data.get('action')
            if action == "get_initial_state":
                await websocket.send_json({"type": "categories_list", "categories": list(CATEGORIES.keys())})
                stats = await get_initial_stats_from_db()
                async with shared_state['lock']: shared_state['stats'].update(stats)
                state = await get_current_app_state()
                await websocket.send_json({"type": "full_state", **state})
            else:
                async with shared_state['lock']: is_busy = shared_state['is_crawling']
                if action == "start_crawl" and not is_busy:
                    asyncio.create_task(crawl_task(data.get('categories', [])))
                elif action == "stop_crawl" and shared_state['is_crawling']:
                    await log_to_ui("收到停止指令，正在安全停止所有任务...", m_type="warning")
                    CRAWL_RUNNING.clear()

                    # 给任务一些时间自然停止
                    await asyncio.sleep(1)

                    # 如果任务还在运行，强制取消
                    if shared_state['is_crawling']:
                        await log_to_ui("强制取消剩余任务...", m_type="warning")
                        FORCE_STOP.set()

                        # 取消所有活跃任务
                        for task in active_tasks.copy():
                            if not task.done():
                                task.cancel()

                        # 等待任务清理
                        await asyncio.sleep(2)

                        # 强制更新状态
                        await update_shared_state({"status": "已停止", "is_crawling": False, "current_category": ""})
    except WebSocketDisconnect: logger.info("客户端断开连接")
    except Exception as e: logger.warning(f"WebSocket 错误: {e}")
    finally: ws_manager.disconnect(websocket)


# --- 数据库查看器 API ---

@app.get("/db_viewer", response_class=HTMLResponse)
async def get_db_viewer_page():
    """提供数据库查看器的HTML页面。"""
    return DB_VIEWER_HTML

@app.get("/api/albums")
async def get_albums(offset: int = 0, limit: int = Query(50, ge=1, le=200)):
    """
    分页获取图集数据，并附带总览统计。
    - 使用 JOIN 和子查询来高效计算每个图集的图片数量。
    - 返回总图集数和总图片数，方便前端展示。
    """
    if not os.path.exists(DB_FILE):
        return JSONResponse(content={"total_albums": 0, "total_images": 0, "albums": []})
        
    async with aiosqlite.connect(DB_FILE) as db:
        db.row_factory = aiosqlite.Row # 让查询结果可以像字典一样访问列

        # 查询分页的图集数据，并计算每个图集的图片数
        query = """
            SELECT
                a.id, a.title, a.url, a.category, a.created_at,
                (SELECT COUNT(i.id) FROM images i WHERE i.album_id = a.id) as image_count
            FROM albums a
            ORDER BY a.id DESC
            LIMIT ? OFFSET ?
        """
        async with db.execute(query, (limit, offset)) as cursor:
            albums_data = [dict(row) for row in await cursor.fetchall()]

        # 查询总览统计数据
        async with db.execute("SELECT COUNT(*) FROM albums") as cursor:
            total_albums = (await cursor.fetchone())[0]
        async with db.execute("SELECT COUNT(*) FROM images") as cursor:
            total_images = (await cursor.fetchone())[0]
            
        return {
            "total_albums": total_albums,
            "total_images": total_images,
            "albums": albums_data
        }

@app.get("/api/album/{album_id}/images")
async def get_album_images(album_id: int):
    """根据图集ID获取其下所有图片链接。"""
    if not os.path.exists(DB_FILE):
        return JSONResponse(content=[])

    async with aiosqlite.connect(DB_FILE) as db:
        db.row_factory = aiosqlite.Row
        query = "SELECT url FROM images WHERE album_id = ? ORDER BY id"
        async with db.execute(query, (album_id,)) as cursor:
            images_data = [dict(row) for row in await cursor.fetchall()]
        return JSONResponse(content=images_data)


if __name__ == "__main__":
    print("="*60)
    print(" Everia Crawler V18.1 (High Performance)")
    print(f" 并发设置: 图集详情页 {MAX_CONCURRENT_ALBUMS}, 分类列表页 {PAGE_SCAN_CONCURRENCY}")
    print(f" 数据将存储在: {os.path.abspath(DB_FILE)}")
    print("="*60)
    print(" 启动成功! 请在浏览器中打开 http://127.0.0.1:8000")
    print("   - 爬虫控制台: http://127.0.0.1:8000/")
    print("   - 数据库查看器: http://127.0.0.1:8000/db_viewer")
    uvicorn.run(app, host="127.0.0.1", port=8000, log_level="warning")