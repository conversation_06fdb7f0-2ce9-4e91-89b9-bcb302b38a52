#!/usr/bin/env python3
"""
测试暂停功能的脚本
用于验证两个下载器的暂停功能是否正常工作
"""

import asyncio
import signal
import sys
import time
from typing import Set

# 模拟全局状态
CRAWL_RUNNING = asyncio.Event()
FORCE_STOP = asyncio.Event()
active_tasks: Set[asyncio.Task] = set()

def signal_handler(signum, frame):
    """处理 Ctrl+C 等信号"""
    print(f"\n收到信号 {signum}，正在强制停止所有任务...")
    FORCE_STOP.set()
    CRAWL_RUNNING.clear()
    
    # 取消所有活跃任务
    for task in active_tasks.copy():
        if not task.done():
            task.cancel()
    
    print("强制退出程序")
    sys.exit(0)

# 注册信号处理器
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

async def mock_download_task(task_id: int, duration: int):
    """模拟下载任务"""
    task = asyncio.current_task()
    active_tasks.add(task)
    
    try:
        print(f"任务 {task_id} 开始，预计运行 {duration} 秒")
        
        for i in range(duration):
            # 检查停止标志
            if not CRAWL_RUNNING.is_set() or FORCE_STOP.is_set():
                print(f"任务 {task_id} 收到停止信号，正在退出...")
                return
            
            # 模拟工作
            await asyncio.sleep(1)
            print(f"任务 {task_id} 进度: {i+1}/{duration}")
        
        print(f"任务 {task_id} 正常完成")
        
    except asyncio.CancelledError:
        print(f"任务 {task_id} 被取消")
        raise
    except Exception as e:
        print(f"任务 {task_id} 异常: {e}")
    finally:
        active_tasks.discard(task)

async def mock_worker(queue: asyncio.Queue, worker_id: int):
    """模拟工作线程"""
    task = asyncio.current_task()
    active_tasks.add(task)
    
    try:
        print(f"Worker {worker_id} 启动")
        
        while CRAWL_RUNNING.is_set() and not FORCE_STOP.is_set():
            try:
                # 使用超时等待，以便能够响应停止信号
                item = await asyncio.wait_for(queue.get(), timeout=1.0)
                if item is None:
                    break
                
                print(f"Worker {worker_id} 处理项目: {item}")
                
                # 模拟处理时间
                for i in range(3):
                    if not CRAWL_RUNNING.is_set() or FORCE_STOP.is_set():
                        print(f"Worker {worker_id} 收到停止信号")
                        return
                    await asyncio.sleep(0.5)
                
                queue.task_done()
                
            except asyncio.TimeoutError:
                # 超时是正常的，继续循环检查停止标志
                continue
            except asyncio.CancelledError:
                print(f"Worker {worker_id} 被取消")
                break
                
    except Exception as e:
        print(f"Worker {worker_id} 异常: {e}")
    finally:
        active_tasks.discard(task)
        print(f"Worker {worker_id} 退出")

async def test_pause_functionality():
    """测试暂停功能"""
    print("开始测试暂停功能...")
    print("按 Ctrl+C 测试暂停功能")
    
    CRAWL_RUNNING.set()
    FORCE_STOP.clear()
    
    # 创建队列和任务
    queue = asyncio.Queue()
    
    # 添加一些工作项目
    for i in range(10):
        await queue.put(f"item_{i}")
    
    # 创建工作线程
    workers = []
    for i in range(3):
        worker = asyncio.create_task(mock_worker(queue, i))
        workers.append(worker)
        active_tasks.add(worker)
    
    # 创建一些长时间运行的任务
    long_tasks = []
    for i in range(2):
        task = asyncio.create_task(mock_download_task(i, 20))
        long_tasks.append(task)
        active_tasks.add(task)
    
    try:
        print(f"活跃任务数: {len(active_tasks)}")
        
        # 等待所有任务完成
        await asyncio.gather(*workers, *long_tasks, return_exceptions=True)
        
    except KeyboardInterrupt:
        print("收到键盘中断")
    except Exception as e:
        print(f"测试异常: {e}")
    finally:
        print("清理剩余任务...")
        
        # 清理队列
        for _ in workers:
            await queue.put(None)
        
        # 取消所有剩余任务
        for task in active_tasks.copy():
            if not task.done():
                task.cancel()
        
        # 等待任务清理
        if active_tasks:
            await asyncio.gather(*active_tasks, return_exceptions=True)
        
        print(f"测试完成，剩余活跃任务数: {len(active_tasks)}")

async def test_graceful_shutdown():
    """测试优雅关闭"""
    print("\n=== 测试优雅关闭 ===")
    
    CRAWL_RUNNING.set()
    FORCE_STOP.clear()
    
    # 创建一些任务
    tasks = []
    for i in range(5):
        task = asyncio.create_task(mock_download_task(i, 10))
        tasks.append(task)
        active_tasks.add(task)
    
    # 运行2秒后停止
    await asyncio.sleep(2)
    print("发送停止信号...")
    CRAWL_RUNNING.clear()
    
    # 给任务一些时间自然停止
    await asyncio.sleep(1)
    
    # 如果任务还在运行，强制取消
    running_tasks = [t for t in tasks if not t.done()]
    if running_tasks:
        print(f"强制取消 {len(running_tasks)} 个剩余任务...")
        FORCE_STOP.set()
        
        for task in running_tasks:
            task.cancel()
        
        await asyncio.gather(*running_tasks, return_exceptions=True)
    
    print("优雅关闭测试完成")

async def main():
    """主函数"""
    print("暂停功能测试程序")
    print("=" * 50)
    
    try:
        # 测试优雅关闭
        await test_graceful_shutdown()
        
        # 重置状态
        active_tasks.clear()
        
        # 测试暂停功能（需要手动按 Ctrl+C）
        await test_pause_functionality()
        
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序异常: {e}")
    finally:
        print("程序退出")

if __name__ == "__main__":
    asyncio.run(main())
